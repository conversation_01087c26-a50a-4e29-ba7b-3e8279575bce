<template>
  <div 
    class="chat-message"
    :class="{ 'user-message': isUser, 'ai-message': !isUser }"
    v-motion
    :initial="{ x: isUser ? 50 : -50, opacity: 0 }"
    :enter="{ x: 0, opacity: 1, transition: { duration: 300 } }"
    :hover="{ y: -2, transition: { duration: 200 } }"
  >
    <!-- AI消息布局 -->
    <div v-if="!isUser" class="message-layout">
      <div class="avatar ai-avatar">
        AI
      </div>
      <div class="message-content-wrapper">
        <div class="message-bubble ai-bubble" :class="{ 'error-bubble': message.isError }">
          <div class="message-text">{{ message.content }}</div>
          <div v-if="message.sources && message.sources.length > 0" class="sources">
            <div class="sources-title">参考来源：</div>
            <div class="sources-list">
              <span 
                v-for="(source, index) in message.sources" 
                :key="index"
                class="source-item"
              >
                {{ source.title || source.name || `来源 ${index + 1}` }}
              </span>
            </div>
          </div>
        </div>
        <div class="message-time">{{ formatTime(message.timestamp) }}</div>
      </div>
    </div>
    
    <!-- 用户消息布局 -->
    <div v-else class="message-layout user-layout">
      <div class="message-content-wrapper user-content">
        <div class="message-bubble user-bubble">
          <div class="message-text">{{ message.content }}</div>
        </div>
        <div class="message-time user-time">{{ formatTime(message.timestamp) }}</div>
      </div>
      <div class="avatar user-avatar">
        用
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { format } from 'date-fns'

const props = defineProps({
  message: {
    type: Object,
    required: true
  }
})

const isUser = computed(() => props.message.type === 'user')

function formatTime(timestamp) {
  return format(new Date(timestamp), 'HH:mm')
}
</script>

<style scoped>
.chat-message {
  width: 100%;
}

.message-layout {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-layout {
  justify-content: flex-end;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ai-avatar {
  background: oklch(0.5500 0.1800 230.0000);
  color: white;
}

.user-avatar {
  background: oklch(0.9200 0.0100 220.0000);
  color: oklch(0.2000 0.0050 264.0000);
}

.message-content-wrapper {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
  line-height: 1.5;
}

.ai-bubble {
  background: oklch(0.9800 0.0050 220.0000);
  color: oklch(0.2000 0.0050 264.0000);
  border-bottom-left-radius: 4px;
}

.user-bubble {
  background: oklch(0.5500 0.1800 230.0000);
  color: white;
  border-bottom-right-radius: 4px;
}

.error-bubble {
  background: oklch(0.6200 0.2000 25.0000 / 0.1);
  border: 1px solid oklch(0.6200 0.2000 25.0000 / 0.3);
  color: oklch(0.6200 0.2000 25.0000);
}

.message-text {
  font-size: 14px;
  white-space: pre-wrap;
}

.message-time {
  font-size: 11px;
  color: oklch(0.5000 0.0100 264.0000);
  margin-left: 4px;
}

.user-time {
  text-align: right;
  margin-left: 0;
  margin-right: 4px;
}

.sources {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid oklch(0.9200 0.0100 220.0000);
}

.sources-title {
  font-size: 12px;
  font-weight: 600;
  color: oklch(0.5000 0.0100 264.0000);
  margin-bottom: 4px;
}

.sources-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.source-item {
  font-size: 11px;
  padding: 2px 6px;
  background: oklch(0.5500 0.1800 230.0000 / 0.1);
  color: oklch(0.5500 0.1800 230.0000);
  border-radius: 4px;
  border: 1px solid oklch(0.5500 0.1800 230.0000 / 0.2);
}

@media (max-width: 768px) {
  .message-content-wrapper {
    max-width: 85%;
  }
}
</style>
