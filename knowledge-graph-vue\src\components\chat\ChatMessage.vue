<template>
  <div
    class="chat-message"
    :class="{ 'user-message': isUser, 'ai-message': !isUser }"
    v-motion
    :initial="{ x: isUser ? 50 : -50, opacity: 0 }"
    :enter="{ x: 0, opacity: 1, transition: { duration: 300 } }"
    :hover="{ y: -2, transition: { duration: 200 } }"
  >
    <!-- AI消息布局 -->
    <div v-if="!isUser" class="message-layout">
      <div class="avatar ai-avatar">AI</div>
      <div class="message-content-wrapper">
        <div class="message-bubble ai-bubble" :class="{ 'error-bubble': message.isError }">
          <div class="message-text markdown-content" v-html="renderedContent"></div>
          <div v-if="message.sources && message.sources.length > 0" class="sources">
            <div class="sources-title">参考来源：</div>
            <div class="sources-list">
              <span v-for="(source, index) in message.sources" :key="index" class="source-item">
                {{ source.title || source.name || `来源 ${index + 1}` }}
              </span>
            </div>
          </div>
        </div>
        <div class="message-time">{{ formatTime(message.timestamp) }}</div>
      </div>
    </div>

    <!-- 用户消息布局 -->
    <div v-else class="message-layout user-layout">
      <div class="message-content-wrapper user-content">
        <div class="message-bubble user-bubble">
          <div class="message-text">{{ message.content }}</div>
        </div>
        <div class="message-time user-time">{{ formatTime(message.timestamp) }}</div>
      </div>
      <div class="avatar user-avatar">用</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { format } from 'date-fns'
import { marked } from 'marked'

const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
})

const isUser = computed(() => props.message.type === 'user')

// 配置marked选项
marked.setOptions({
  breaks: true, // 支持换行
  gfm: true, // GitHub风格markdown
})

// 渲染markdown内容
const renderedContent = computed(() => {
  if (props.message.type === 'ai') {
    return marked(props.message.content)
  }
  return props.message.content
})

function formatTime(timestamp) {
  return format(new Date(timestamp), 'HH:mm')
}
</script>

<style scoped>
.chat-message {
  width: 100%;
}

.message-layout {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-layout {
  justify-content: flex-end;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ai-avatar {
  background: oklch(0.55 0.18 230);
  color: white;
}

.user-avatar {
  background: oklch(0.92 0.01 220);
  color: oklch(0.2 0.005 264);
}

.message-content-wrapper {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
  line-height: 1.5;
}

.ai-bubble {
  background: oklch(0.98 0.005 220);
  color: oklch(0.2 0.005 264);
  border-bottom-left-radius: 4px;
}

.user-bubble {
  background: oklch(0.55 0.18 230);
  color: white;
  border-bottom-right-radius: 4px;
}

.error-bubble {
  background: oklch(0.62 0.2 25 / 0.1);
  border: 1px solid oklch(0.62 0.2 25 / 0.3);
  color: oklch(0.62 0.2 25);
}

.message-text {
  font-size: 14px;
  white-space: pre-wrap;
}

/* Markdown样式 */
.markdown-content {
  line-height: 1.6;
  white-space: normal;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 700;
  color: oklch(0.15 0.005 264);
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 20px;
  border-bottom: 2px solid oklch(0.9 0.01 220);
  padding-bottom: 4px;
}

.markdown-content h2 {
  font-size: 18px;
  border-bottom: 1px solid oklch(0.92 0.01 220);
  padding-bottom: 2px;
}

.markdown-content h3 {
  font-size: 16px;
}

.markdown-content h4 {
  font-size: 15px;
}

.markdown-content h5 {
  font-size: 14px;
}

.markdown-content h6 {
  font-size: 13px;
  color: oklch(0.4 0.01 264);
}

.markdown-content p {
  margin: 8px 0;
}

.markdown-content code {
  background: oklch(0.95 0.01 220);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  color: oklch(0.3 0.005 264);
}

.markdown-content pre {
  background: oklch(0.95 0.01 220);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
  border: 1px solid oklch(0.92 0.01 220);
}

.markdown-content pre code {
  background: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin: 6px 0;
  line-height: 1.5;
}

.markdown-content li::marker {
  color: oklch(0.55 0.18 230);
  font-weight: 600;
}

.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin: 4px 0;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

.markdown-content blockquote {
  border-left: 3px solid oklch(0.55 0.18 230);
  padding-left: 12px;
  margin: 8px 0;
  color: oklch(0.5 0.01 264);
  font-style: italic;
}

.markdown-content strong,
.markdown-content b {
  font-weight: 700;
  color: oklch(0.1 0.005 264);
}

.markdown-content em,
.markdown-content i {
  font-style: italic;
  color: oklch(0.25 0.005 264);
}

.markdown-content a {
  color: oklch(0.55 0.18 230);
  text-decoration: underline;
}

.markdown-content a:hover {
  color: oklch(0.45 0.18 230);
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid oklch(0.92 0.01 220);
  padding: 6px 8px;
  text-align: left;
}

.markdown-content th {
  background: oklch(0.95 0.01 220);
  font-weight: 600;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid oklch(0.92 0.01 220);
  margin: 16px 0;
}

.message-time {
  font-size: 11px;
  color: oklch(0.5 0.01 264);
  margin-left: 4px;
}

.user-time {
  text-align: right;
  margin-left: 0;
  margin-right: 4px;
}

.sources {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid oklch(0.92 0.01 220);
}

.sources-title {
  font-size: 12px;
  font-weight: 600;
  color: oklch(0.5 0.01 264);
  margin-bottom: 4px;
}

.sources-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.source-item {
  font-size: 11px;
  padding: 2px 6px;
  background: oklch(0.55 0.18 230 / 0.1);
  color: oklch(0.55 0.18 230);
  border-radius: 4px;
  border: 1px solid oklch(0.55 0.18 230 / 0.2);
}

@media (max-width: 768px) {
  .message-content-wrapper {
    max-width: 85%;
  }
}
</style>
