// API配置和工具函数

// RAG聊天模块API地址
const RAG_API_BASE_URL = import.meta.env.VITE_RAG_API_BASE_URL || 'http://localhost:8000'

// 知识图谱模块API地址（如果需要）
const GRAPH_API_BASE_URL = import.meta.env.VITE_GRAPH_API_BASE_URL || 'http://localhost:3001'

// 创建RAG聊天API请求函数
async function ragApiRequest(endpoint, options = {}) {
  const url = `${RAG_API_BASE_URL}${endpoint}`

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  }

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  }

  try {
    const response = await fetch(url, mergedOptions)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      return await response.json()
    }

    return await response.text()
  } catch (error) {
    console.error(`API请求失败 [${endpoint}]:`, error)
    throw error
  }
}

// RAG聊天相关API
export const chatAPI = {
  // 获取系统配置
  async getConfig() {
    return ragApiRequest('/api/v1/conversation/config')
  },

  // 发送聊天消息
  async sendMessage(data) {
    return ragApiRequest('/api/v1/conversation/chat', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // 清除对话
  async clearConversation(conversationId) {
    return ragApiRequest(`/api/v1/conversation/conversations/${conversationId}`, {
      method: 'DELETE',
    })
  },

  // 获取对话状态
  async getConversationStatus(conversationId) {
    return ragApiRequest(`/api/v1/conversation/conversations/${conversationId}/status`)
  },
}

// 导出API基础URL供其他地方使用
export { RAG_API_BASE_URL, GRAPH_API_BASE_URL }
