<template>
  <Teleport to="body">
    <div 
      v-if="visible"
      class="dialog-overlay"
      @click="handleOverlayClick"
      v-motion
      :initial="{ opacity: 0 }"
      :enter="{ opacity: 1, transition: { duration: 200 } }"
      :leave="{ opacity: 0, transition: { duration: 200 } }"
    >
      <div 
        class="dialog-container"
        @click.stop
        v-motion
        :initial="{ y: -50, opacity: 0, scale: 0.9 }"
        :enter="{ y: 0, opacity: 1, scale: 1, transition: { duration: 300, delay: 100 } }"
        :leave="{ y: -50, opacity: 0, scale: 0.9, transition: { duration: 200 } }"
      >
        <div class="dialog-header">
          <h3 class="dialog-title">{{ title }}</h3>
        </div>
        
        <div class="dialog-content">
          <p class="dialog-message">{{ message }}</p>
        </div>
        
        <div class="dialog-actions">
          <button 
            @click="handleCancel"
            class="cancel-button"
            v-motion
            :initial="{ scale: 1 }"
            :hover="{ scale: 1.02 }"
            :tap="{ scale: 0.98 }"
          >
            取消
          </button>
          <button 
            @click="handleConfirm"
            class="confirm-button"
            v-motion
            :initial="{ scale: 1 }"
            :hover="{ scale: 1.02 }"
            :tap="{ scale: 0.98 }"
          >
            确认
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '确认'
  },
  message: {
    type: String,
    default: '确定要执行此操作吗？'
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

function handleOverlayClick() {
  handleCancel()
}

function handleCancel() {
  emit('update:visible', false)
  emit('cancel')
}

function handleConfirm() {
  emit('update:visible', false)
  emit('confirm')
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.dialog-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  max-width: 400px;
  width: 100%;
  overflow: hidden;
}

.dialog-header {
  padding: 20px 20px 0 20px;
}

.dialog-title {
  font-size: 16px;
  font-weight: 600;
  color: oklch(0.2000 0.0050 264.0000);
  margin: 0;
}

.dialog-content {
  padding: 16px 20px;
}

.dialog-message {
  font-size: 14px;
  color: oklch(0.5000 0.0100 264.0000);
  line-height: 1.5;
  margin: 0;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 0 20px 20px 20px;
  justify-content: flex-end;
}

.cancel-button,
.confirm-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  min-width: 80px;
}

.cancel-button {
  background: white;
  color: oklch(0.5000 0.0100 264.0000);
  border-color: oklch(0.9200 0.0100 220.0000);
}

.cancel-button:hover {
  background: oklch(0.9800 0.0050 220.0000);
  border-color: oklch(0.8000 0.0100 220.0000);
}

.confirm-button {
  background: oklch(0.6200 0.2000 25.0000);
  color: white;
  border-color: oklch(0.6200 0.2000 25.0000);
}

.confirm-button:hover {
  background: oklch(0.5800 0.2000 25.0000);
  border-color: oklch(0.5800 0.2000 25.0000);
  box-shadow: 0 4px 12px oklch(0.6200 0.2000 25.0000 / 0.3);
}

@media (max-width: 480px) {
  .dialog-container {
    margin: 0 10px;
  }
  
  .dialog-actions {
    flex-direction: column;
  }
  
  .cancel-button,
  .confirm-button {
    width: 100%;
  }
}
</style>
