<template>
  <div class="debug-panel">
    <!-- 标题栏 -->
    <div class="debug-header" @click="toggleExpanded" :class="{ expanded: isExpanded }">
      <div class="debug-title">
        <CogIcon class="w-4 h-4" />
        <span>调试模式</span>
      </div>
      <ChevronDownIcon
        class="w-4 h-4 transition-transform duration-200"
        :class="{ 'rotate-180': isExpanded }"
      />
    </div>

    <!-- 内容区 -->
    <div
      v-show="isExpanded"
      class="debug-content"
      v-motion
      :initial="{ height: 0, opacity: 0 }"
      :enter="{ height: 'auto', opacity: 1, transition: { duration: 300 } }"
      :leave="{ height: 0, opacity: 0, transition: { duration: 300 } }"
    >
      <div class="input-group">
        <div class="input-field">
          <label for="conversation-id">Conversation ID</label>
          <input
            id="conversation-id"
            type="text"
            :value="conversationId"
            readonly
            class="debug-input readonly-input"
            :title="conversationId"
          />
        </div>

        <div class="input-field">
          <label for="course-id">Course ID</label>
          <input
            id="course-id"
            type="text"
            :value="courseId"
            @input="$emit('update:courseId', $event.target.value)"
            placeholder="输入课程ID"
            class="debug-input"
          />
        </div>

        <div class="input-field">
          <label for="material-id">Material ID</label>
          <input
            id="material-id"
            type="text"
            :value="materialId"
            @input="$emit('update:materialId', $event.target.value)"
            placeholder="输入材料ID"
            class="debug-input"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { CogIcon, ChevronDownIcon } from '@heroicons/vue/24/outline'

defineProps({
  conversationId: {
    type: String,
    default: '',
  },
  courseId: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
})

defineEmits(['update:courseId', 'update:materialId'])

const isExpanded = ref(false)

function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.debug-panel {
  border-bottom: 1px solid oklch(0.92 0.01 220);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: oklch(0.98 0.005 220);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.debug-header:hover {
  background: oklch(0.55 0.18 230);
  color: white;
}

.debug-header.expanded {
  background: oklch(0.55 0.18 230);
  color: white;
}

.debug-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
}

.debug-content {
  padding: 16px;
  background: white;
  overflow: hidden;
}

.input-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-field label {
  font-size: 12px;
  font-weight: 500;
  color: #000000;
}

.debug-input {
  padding: 8px 12px;
  border: 1px solid oklch(0.92 0.01 220);
  border-radius: 4px;
  background: oklch(0.98 0.005 220);
  font-size: 14px;
  color: #000000;
  transition: all 0.2s ease;
}

.debug-input:focus {
  outline: none;
  border-color: oklch(0.55 0.18 230);
  box-shadow: 0 0 0 3px oklch(0.55 0.18 230 / 0.1);
}

.readonly-input {
  background: #f5f5f5 !important;
  cursor: default;
  font-family: monospace;
  font-size: 12px;
}

.readonly-input:focus {
  border-color: oklch(0.92 0.01 220) !important;
  box-shadow: none !important;
}

.debug-input::placeholder {
  color: #666666;
}

@media (max-width: 768px) {
  .input-group {
    grid-template-columns: 1fr;
  }
}
</style>
