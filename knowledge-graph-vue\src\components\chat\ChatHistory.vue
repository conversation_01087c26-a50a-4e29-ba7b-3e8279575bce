<template>
  <div class="chat-history">
    <!-- 头部 -->
    <div class="chat-header">
      <button 
        @click="$emit('clearConversation')"
        class="clear-button"
        v-motion
        :initial="{ scale: 1 }"
        :hover="{ scale: 1.05 }"
        :tap="{ scale: 0.95 }"
      >
        <TrashIcon class="w-4 h-4" />
        <span>清除对话</span>
      </button>
    </div>
    
    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div class="messages-list">
        <ChatMessage
          v-for="message in messages"
          :key="message.id"
          :message="message"
        />
        
        <!-- 打字指示器 -->
        <TypingIndicator v-if="isTyping" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { TrashIcon } from '@heroicons/vue/24/outline'
import ChatMessage from './ChatMessage.vue'
import TypingIndicator from './TypingIndicator.vue'

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  isTyping: {
    type: Boolean,
    default: false
  }
})

defineEmits(['clearConversation'])

const messagesContainer = ref(null)

// 自动滚动到底部
async function scrollToBottom() {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 监听消息变化，自动滚动
watch(() => props.messages.length, scrollToBottom)
watch(() => props.isTyping, scrollToBottom)
</script>

<style scoped>
.chat-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-bottom: 1px solid oklch(0.9200 0.0100 220.0000);
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid oklch(0.9200 0.0100 220.0000);
  border-radius: 4px;
  color: oklch(0.5000 0.0100 264.0000);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  border-color: oklch(0.6200 0.2000 25.0000);
  color: oklch(0.6200 0.2000 25.0000);
  background: oklch(0.6200 0.2000 25.0000 / 0.05);
}

.clear-button:hover .w-4 {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}

.messages-container::-webkit-scrollbar {
  width: 4px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: oklch(0.9200 0.0100 220.0000);
  border-radius: 2px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: oklch(0.8000 0.0100 220.0000);
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
  min-height: 100%;
}
</style>
