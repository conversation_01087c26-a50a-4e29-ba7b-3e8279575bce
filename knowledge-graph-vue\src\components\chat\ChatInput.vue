<template>
  <div class="chat-input">
    <!-- 输入框 -->
    <div class="input-container">
      <textarea
        ref="textareaRef"
        v-model="inputText"
        @keydown="handleKeydown"
        @input="adjustHeight"
        placeholder="输入您的问题..."
        class="message-input"
        :disabled="isSending"
        rows="1"
      ></textarea>
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
      <!-- 模式选择 -->
      <select
        :value="mode"
        @change="$emit('update:mode', $event.target.value)"
        class="mode-select"
        :disabled="isSending"
      >
        <option value="condense_plus_context">根据课件</option>
        <option value="simple">通用知识</option>
      </select>

      <!-- 发送按钮 -->
      <button
        @click="handleSend"
        :disabled="!canSend"
        class="send-button"
        :class="{ sending: isSending }"
        v-motion
        :initial="{ scale: 1 }"
        :hover="{ scale: canSend ? 1.05 : 1 }"
        :tap="{ scale: canSend ? 0.95 : 1 }"
      >
        <PaperAirplaneIcon class="w-4 h-4" :class="{ 'animate-pulse': isSending }" />
        <span v-if="!isSending">发送</span>
        <span v-else>发送中...</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { PaperAirplaneIcon } from '@heroicons/vue/24/solid'

const props = defineProps({
  mode: {
    type: String,
    default: 'condense_plus_context',
  },
  isSending: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:mode', 'sendMessage'])

const inputText = ref('')
const textareaRef = ref(null)

const canSend = computed(() => {
  return inputText.value.trim().length > 0 && !props.isSending
})

// 自动调整高度
async function adjustHeight() {
  await nextTick()
  const textarea = textareaRef.value
  if (textarea) {
    textarea.style.height = 'auto'
    const maxHeight = 10 * 24 // 10行的高度
    const newHeight = Math.min(textarea.scrollHeight, maxHeight)
    textarea.style.height = newHeight + 'px'
  }
}

// 处理键盘事件
function handleKeydown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSend()
  }
}

// 发送消息
function handleSend() {
  if (!canSend.value) return

  const message = inputText.value.trim()
  if (message) {
    emit('sendMessage', message)
    inputText.value = ''
    adjustHeight()
  }
}
</script>

<style scoped>
.chat-input {
  border-top: 1px solid oklch(0.92 0.01 220);
  background: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-container {
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 40px;
  max-height: 240px;
  padding: 8px 12px;
  border: 1px solid oklch(0.92 0.01 220);
  border-radius: 4px;
  background: oklch(0.98 0.005 220);
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  color: #000000;
  resize: none;
  overflow-y: auto;
  transition: all 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: oklch(0.55 0.18 230);
  box-shadow: 0 0 0 3px oklch(0.55 0.18 230 / 0.1);
  background: white;
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.message-input::placeholder {
  color: oklch(0.5 0.01 264);
}

.message-input::-webkit-scrollbar {
  width: 4px;
}

.message-input::-webkit-scrollbar-track {
  background: transparent;
}

.message-input::-webkit-scrollbar-thumb {
  background: oklch(0.92 0.01 220);
  border-radius: 2px;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.mode-select {
  padding: 6px 12px;
  border: 1px solid oklch(0.92 0.01 220);
  border-radius: 4px;
  background: white;
  font-size: 12px;
  color: oklch(0.2 0.005 264);
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-select:focus {
  outline: none;
  border-color: oklch(0.55 0.18 230);
  box-shadow: 0 0 0 3px oklch(0.55 0.18 230 / 0.1);
}

.mode-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: oklch(0.55 0.18 230);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  background: oklch(0.5 0.18 230);
  box-shadow: 0 4px 12px oklch(0.55 0.18 230 / 0.3);
}

.send-button:disabled {
  background: oklch(0.92 0.01 220);
  color: oklch(0.5 0.01 264);
  cursor: not-allowed;
  box-shadow: none;
}

.send-button.sending {
  background: oklch(0.55 0.18 230);
  cursor: wait;
}

@media (max-width: 768px) {
  .control-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .send-button {
    width: 100%;
  }
}
</style>
