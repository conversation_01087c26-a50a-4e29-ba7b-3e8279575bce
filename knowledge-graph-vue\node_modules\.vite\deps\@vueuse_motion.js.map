{"version": 3, "sources": ["../../defu/dist/defu.mjs", "../../@vueuse/shared/index.mjs", "../../@vueuse/core/index.mjs", "../../framesync/dist/es/on-next-frame.mjs", "../../framesync/dist/es/create-render-step.mjs", "../../framesync/dist/es/index.mjs", "../../tslib/tslib.es6.js", "../../hey-listen/dist/hey-listen.es.js", "../../popmotion/dist/es/utils/clamp.mjs", "../../popmotion/dist/es/animations/utils/find-spring.mjs", "../../popmotion/dist/es/animations/generators/spring.mjs", "../../popmotion/dist/es/utils/progress.mjs", "../../popmotion/dist/es/utils/mix.mjs", "../../style-value-types/dist/es/utils.mjs", "../../style-value-types/dist/es/numbers/index.mjs", "../../style-value-types/dist/es/numbers/units.mjs", "../../style-value-types/dist/es/color/utils.mjs", "../../style-value-types/dist/es/color/hsla.mjs", "../../style-value-types/dist/es/color/rgba.mjs", "../../style-value-types/dist/es/color/hex.mjs", "../../style-value-types/dist/es/color/index.mjs", "../../style-value-types/dist/es/complex/index.mjs", "../../style-value-types/dist/es/complex/filter.mjs", "../../popmotion/dist/es/utils/hsla-to-rgba.mjs", "../../popmotion/dist/es/utils/mix-color.mjs", "../../popmotion/dist/es/utils/inc.mjs", "../../popmotion/dist/es/utils/pipe.mjs", "../../popmotion/dist/es/utils/mix-complex.mjs", "../../popmotion/dist/es/utils/interpolate.mjs", "../../popmotion/dist/es/easing/utils.mjs", "../../popmotion/dist/es/easing/index.mjs", "../../popmotion/dist/es/animations/generators/keyframes.mjs", "../../popmotion/dist/es/animations/generators/decay.mjs", "../../popmotion/dist/es/animations/utils/detect-animation-from-options.mjs", "../../popmotion/dist/es/animations/utils/elapsed.mjs", "../../popmotion/dist/es/animations/index.mjs", "../../popmotion/dist/es/utils/velocity-per-second.mjs", "../../popmotion/dist/es/animations/inertia.mjs", "../../popmotion/dist/es/utils/attract.mjs", "../../popmotion/dist/es/easing/cubic-bezier.mjs", "../../@vueuse/motion/dist/index.mjs"], "sourcesContent": ["function isPlainObject(value) {\n  if (value === null || typeof value !== \"object\") {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(value);\n  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {\n    return false;\n  }\n  if (Symbol.iterator in value) {\n    return false;\n  }\n  if (Symbol.toStringTag in value) {\n    return Object.prototype.toString.call(value) === \"[object Module]\";\n  }\n  return true;\n}\n\nfunction _defu(baseObject, defaults, namespace = \".\", merger) {\n  if (!isPlainObject(defaults)) {\n    return _defu(baseObject, {}, namespace, merger);\n  }\n  const object = Object.assign({}, defaults);\n  for (const key in baseObject) {\n    if (key === \"__proto__\" || key === \"constructor\") {\n      continue;\n    }\n    const value = baseObject[key];\n    if (value === null || value === void 0) {\n      continue;\n    }\n    if (merger && merger(object, key, value, namespace)) {\n      continue;\n    }\n    if (Array.isArray(value) && Array.isArray(object[key])) {\n      object[key] = [...value, ...object[key]];\n    } else if (isPlainObject(value) && isPlainObject(object[key])) {\n      object[key] = _defu(\n        value,\n        object[key],\n        (namespace ? `${namespace}.` : \"\") + key.toString(),\n        merger\n      );\n    } else {\n      object[key] = value;\n    }\n  }\n  return object;\n}\nfunction createDefu(merger) {\n  return (...arguments_) => (\n    // eslint-disable-next-line unicorn/no-array-reduce\n    arguments_.reduce((p, c) => _defu(p, c, \"\", merger), {})\n  );\n}\nconst defu = createDefu();\nconst defuFn = createDefu((object, key, currentValue) => {\n  if (object[key] !== void 0 && typeof currentValue === \"function\") {\n    object[key] = currentValue(object[key]);\n    return true;\n  }\n});\nconst defuArrayFn = createDefu((object, key, currentValue) => {\n  if (Array.isArray(object[key]) && typeof currentValue === \"function\") {\n    object[key] = currentValue(object[key]);\n    return true;\n  }\n});\n\nexport { createDefu, defu as default, defu, defuArrayFn, defuFn };\n", "import { shallowRef, watchEffect, readonly, watch, customRef, getCurrentScope, onScopeDispose, effectScope, getCurrentInstance, hasInjectionContext, inject, provide, ref, isRef, unref, toValue as toValue$1, computed, reactive, toRefs as toRefs$1, toRef as toRef$1, shallowReadonly, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue';\n\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, {\n    ...options,\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  });\n  return readonly(result);\n}\n\nfunction computedWithControl(source, fn, options = {}) {\n  let v = void 0;\n  let track;\n  let trigger;\n  let dirty = true;\n  const update = () => {\n    dirty = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\", ...options });\n  const get = typeof fn === \"function\" ? fn : fn.get;\n  const set = typeof fn === \"function\" ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty) {\n          v = get(v);\n          dirty = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createEventHook() {\n  const fns = /* @__PURE__ */ new Set();\n  const off = (fn) => {\n    fns.delete(fn);\n  };\n  const clear = () => {\n    fns.clear();\n  };\n  const on = (fn) => {\n    fns.add(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (...args) => {\n    return Promise.all(Array.from(fns).map((fn) => fn(...args)));\n  };\n  return {\n    on,\n    off,\n    trigger,\n    clear\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return (...args) => {\n    if (!initialized) {\n      state = scope.run(() => stateFactory(...args));\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nconst localProvidedStateMap = /* @__PURE__ */ new WeakMap();\n\nconst injectLocal = /* @__NO_SIDE_EFFECTS__ */ (...args) => {\n  var _a;\n  const key = args[0];\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null && !hasInjectionContext())\n    throw new Error(\"injectLocal must be called in setup\");\n  if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))\n    return localProvidedStateMap.get(instance)[key];\n  return inject(...args);\n};\n\nfunction provideLocal(key, value) {\n  var _a;\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"provideLocal must be called in setup\");\n  if (!localProvidedStateMap.has(instance))\n    localProvidedStateMap.set(instance, /* @__PURE__ */ Object.create(null));\n  const localProvidedState = localProvidedStateMap.get(instance);\n  localProvidedState[key] = value;\n  return provide(key, value);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createInjectionState(composable, options) {\n  const key = (options == null ? void 0 : options.injectionKey) || Symbol(composable.name || \"InjectionState\");\n  const defaultValue = options == null ? void 0 : options.defaultValue;\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provideLocal(key, state);\n    return state;\n  };\n  const useInjectedState = () => injectLocal(key, defaultValue);\n  return [useProvidingState, useInjectedState];\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createRef(value, deep) {\n  if (deep === true) {\n    return ref(value);\n  } else {\n    return shallowRef(value);\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!scope) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = { ...obj };\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : toValue$1;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(\n    keys.map((key) => {\n      const value = obj[key];\n      return [\n        key,\n        typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n      ];\n    })\n  );\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => !predicate(toValue$1(v), k))) : Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nconst isClient = typeof window !== \"undefined\" && typeof document !== \"undefined\";\nconst isWorker = typeof WorkerGlobalScope !== \"undefined\" && globalThis instanceof WorkerGlobalScope;\nconst isDef = (val) => typeof val !== \"undefined\";\nconst notNullish = (val) => val != null;\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nconst isIOS = /* @__PURE__ */ getIsIOS();\nfunction getIsIOS() {\n  var _a, _b;\n  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));\n}\n\nfunction toRef(...args) {\n  if (args.length !== 1)\n    return toRef$1(...args);\n  const r = args[0];\n  return typeof r === \"function\" ? readonly(customRef(() => ({ get: r, set: noop }))) : ref(r);\n}\nconst resolveRef = toRef;\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => predicate(toValue$1(v), k))) : Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = toValue$1(defaultValue);\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = toValue$1(defaultValue);\n      trigger();\n    }, toValue$1(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  let lastInvoker;\n  const filter = (invoke) => {\n    const duration = toValue$1(ms);\n    const maxDuration = toValue$1(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = void 0;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      lastInvoker = invoke;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = void 0;\n          resolve(lastInvoker());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = void 0;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(...args) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  let ms;\n  let trailing;\n  let leading;\n  let rejectOnCancel;\n  if (!isRef(args[0]) && typeof args[0] === \"object\")\n    ({ delay: ms, trailing = true, leading = true, rejectOnCancel = false } = args[0]);\n  else\n    [ms, trailing = true, leading = true, rejectOnCancel = false] = args;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = toValue$1(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter, options = {}) {\n  const {\n    initialState = \"active\"\n  } = options;\n  const isActive = toRef(initialState === \"active\");\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?\\d+\\.?\\d*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = Number.parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction pxValue(px) {\n  return px.endsWith(\"rem\") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction objectOmit(obj, keys, omitUndefined = false) {\n  return Object.fromEntries(Object.entries(obj).filter(([key, value]) => {\n    return (!omitUndefined || value !== void 0) && !keys.includes(key);\n  }));\n}\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\nfunction toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nfunction cacheStringFunction(fn) {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\nconst hyphenateRE = /\\B([A-Z])/g;\nconst hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nconst camelizeRE = /-(\\w)/g;\nconst camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\n\nfunction getLifeCycleTarget(target) {\n  return target || getCurrentInstance();\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(\n    debounceFilter(ms, options),\n    fn\n  );\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(toValue$1(value));\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return shallowReadonly(debounced);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(\n    throttleFilter(ms, trailing, leading, rejectOnCancel),\n    fn\n  );\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(toValue$1(value));\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(\n    ref,\n    {\n      get,\n      set,\n      untrackedGet,\n      silentSet,\n      peek,\n      lay\n    },\n    { enumerable: true }\n  );\n}\nconst controlledRef = refWithControl;\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    const [target, key, value] = args;\n    target[key] = value;\n  }\n}\n\nfunction watchWithFilter(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  return watch(\n    source,\n    createFilterWrapper(\n      eventFilter,\n      cb\n    ),\n    watchOptions\n  );\n}\n\nfunction watchPausable(source, cb, options = {}) {\n  const {\n    eventFilter: filter,\n    initialState = \"active\",\n    ...watchOptions\n  } = options;\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter, { initialState });\n  const stop = watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter\n    }\n  );\n  return { stop, pause, resume, isActive };\n}\n\nfunction syncRef(left, right, ...[options]) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options || {};\n  const watchers = [];\n  const transformLTR = \"ltr\" in transform && transform.ltr || ((v) => v);\n  const transformRTL = \"rtl\" in transform && transform.rtl || ((v) => v);\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchers.push(watchPausable(\n      left,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        right.value = transformLTR(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchers.push(watchPausable(\n      right,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        left.value = transformRTL(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  const stop = () => {\n    watchers.forEach((w) => w.stop());\n  };\n  return stop;\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  const targetsArray = toArray(targets);\n  return watch(\n    source,\n    (newValue) => targetsArray.forEach((target) => target.value = newValue),\n    { flush, deep, immediate }\n  );\n}\n\nfunction toRefs(objectRef, options = {}) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        var _a;\n        const replaceRef = (_a = toValue$1(options.replaceRef)) != null ? _a : true;\n        if (replaceRef) {\n          if (Array.isArray(objectRef.value)) {\n            const copy = [...objectRef.value];\n            copy[key] = v;\n            objectRef.value = copy;\n          } else {\n            const newObject = { ...objectRef.value, [key]: v };\n            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));\n            objectRef.value = newObject;\n          }\n        } else {\n          objectRef.value[key] = v;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nconst toValue = toValue$1;\nconst resolveUnref = toValue$1;\n\nfunction tryOnBeforeMount(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeMount(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeUnmount(fn, target);\n}\n\nfunction tryOnMounted(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onMounted(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onUnmounted(fn, target);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        r,\n        (v) => {\n          if (condition(v) !== isNot) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => stop == null ? void 0 : stop())\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        [r, value],\n        ([v1, v2]) => {\n          if (isNot !== (v1 === v2)) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v1);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => {\n          stop == null ? void 0 : stop();\n          return toValue$1(r);\n        })\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(toValue$1(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(toValue$1(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction defaultComparator(value, othVal) {\n  return value === othVal;\n}\n// @__NO_SIDE_EFFECTS__\nfunction useArrayDifference(...args) {\n  var _a, _b;\n  const list = args[0];\n  const values = args[1];\n  let compareFn = (_a = args[2]) != null ? _a : defaultComparator;\n  const {\n    symmetric = false\n  } = (_b = args[3]) != null ? _b : {};\n  if (typeof compareFn === \"string\") {\n    const key = compareFn;\n    compareFn = (value, othVal) => value[key] === othVal[key];\n  }\n  const diff1 = computed(() => toValue$1(list).filter((x) => toValue$1(values).findIndex((y) => compareFn(x, y)) === -1));\n  if (symmetric) {\n    const diff2 = computed(() => toValue$1(values).filter((x) => toValue$1(list).findIndex((y) => compareFn(x, y)) === -1));\n    return computed(() => symmetric ? [...toValue$1(diff1), ...toValue$1(diff2)] : toValue$1(diff1));\n  } else {\n    return diff1;\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayEvery(list, fn) {\n  return computed(() => toValue$1(list).every((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayFilter(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).filter(fn));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayFind(list, fn) {\n  return computed(() => toValue$1(\n    toValue$1(list).find((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => toValue$1(list).findIndex((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\n// @__NO_SIDE_EFFECTS__\nfunction useArrayFindLast(list, fn) {\n  return computed(() => toValue$1(\n    !Array.prototype.findLast ? findLast(toValue$1(list), (element, index, array) => fn(toValue$1(element), index, array)) : toValue$1(list).findLast((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction isArrayIncludesOptions(obj) {\n  return isObject(obj) && containsProp(obj, \"formIndex\", \"comparator\");\n}\n// @__NO_SIDE_EFFECTS__\nfunction useArrayIncludes(...args) {\n  var _a;\n  const list = args[0];\n  const value = args[1];\n  let comparator = args[2];\n  let formIndex = 0;\n  if (isArrayIncludesOptions(comparator)) {\n    formIndex = (_a = comparator.fromIndex) != null ? _a : 0;\n    comparator = comparator.comparator;\n  }\n  if (typeof comparator === \"string\") {\n    const key = comparator;\n    comparator = (element, value2) => element[key] === toValue$1(value2);\n  }\n  comparator = comparator != null ? comparator : (element, value2) => element === toValue$1(value2);\n  return computed(() => toValue$1(list).slice(formIndex).some((element, index, array) => comparator(\n    toValue$1(element),\n    toValue$1(value),\n    index,\n    toValue$1(array)\n  )));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayJoin(list, separator) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).join(toValue$1(separator)));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayMap(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).map(fn));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(toValue$1(sum), toValue$1(value), index);\n  return computed(() => {\n    const resolved = toValue$1(list);\n    return args.length ? resolved.reduce(reduceCallback, typeof args[0] === \"function\" ? toValue$1(args[0]()) : toValue$1(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useArraySome(list, fn) {\n  return computed(() => toValue$1(list).some((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction uniq(array) {\n  return Array.from(new Set(array));\n}\nfunction uniqueElementsBy(array, fn) {\n  return array.reduce((acc, v) => {\n    if (!acc.some((x) => fn(v, x, array)))\n      acc.push(v);\n    return acc;\n  }, []);\n}\n// @__NO_SIDE_EFFECTS__\nfunction useArrayUnique(list, compareFn) {\n  return computed(() => {\n    const resolvedList = toValue$1(list).map((element) => toValue$1(element));\n    return compareFn ? uniqueElementsBy(resolvedList, compareFn) : uniq(resolvedList);\n  });\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  let _initialValue = unref(initialValue);\n  const count = shallowRef(initialValue);\n  const {\n    max = Number.POSITIVE_INFINITY,\n    min = Number.NEGATIVE_INFINITY\n  } = options;\n  const inc = (delta = 1) => count.value = Math.max(Math.min(max, count.value + delta), min);\n  const dec = (delta = 1) => count.value = Math.min(Math.max(min, count.value - delta), max);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = _initialValue) => {\n    _initialValue = val;\n    return set(val);\n  };\n  return { count: shallowReadonly(count), inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[T\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/i;\nconst REGEX_FORMAT = /[YMDHhms]o|\\[([^\\]]+)\\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;\nfunction defaultMeridiem(hours, minutes, isLowercase, hasPeriod) {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n}\nfunction formatOrdinal(num) {\n  const suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n  const v = num % 100;\n  return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n}\nfunction formatDate(date, formatStr, options = {}) {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const stripTimeZone = (dateString) => {\n    var _a2;\n    return (_a2 = dateString.split(\" \")[1]) != null ? _a2 : \"\";\n  };\n  const matches = {\n    Yo: () => formatOrdinal(years),\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    Mo: () => formatOrdinal(month + 1),\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"long\" }),\n    D: () => String(days),\n    Do: () => formatOrdinal(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    Ho: () => formatOrdinal(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    ho: () => formatOrdinal(hours % 12 || 12),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mo: () => formatOrdinal(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    so: () => formatOrdinal(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true),\n    z: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"longOffset\" }))\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => {\n    var _a2, _b;\n    return (_b = $1 != null ? $1 : (_a2 = matches[match]) == null ? void 0 : _a2.call(matches)) != null ? _b : match;\n  });\n}\nfunction normalizeDate(date) {\n  if (date === null)\n    return new Date(Number.NaN);\n  if (date === void 0)\n    return /* @__PURE__ */ new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n}\n// @__NO_SIDE_EFFECTS__\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(toValue$1(date)), toValue$1(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = shallowRef(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = toValue$1(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    if (isActive.value)\n      timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || typeof interval === \"function\") {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive: shallowReadonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = shallowRef(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(\n    callback ? () => {\n      update();\n      callback(counter.value);\n    } : update,\n    interval,\n    { immediate }\n  );\n  if (exposeControls) {\n    return {\n      counter: shallowReadonly(counter),\n      reset,\n      ...controls\n    };\n  } else {\n    return shallowReadonly(counter);\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = shallowRef((_a = options.initialValue) != null ? _a : null);\n  watch(\n    source,\n    () => ms.value = timestamp(),\n    options\n  );\n  return shallowReadonly(ms);\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const isPending = shallowRef(false);\n  let timer;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    if (immediateCallback)\n      cb();\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = void 0;\n      cb(...args);\n    }, toValue$1(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: shallowReadonly(isPending),\n    start,\n    stop\n  };\n}\n\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(\n    callback != null ? callback : noop,\n    interval,\n    options\n  );\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return {\n      ready,\n      ...controls\n    };\n  } else {\n    return ready;\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = toValue$1(value);\n    if (typeof method === \"function\")\n      resolved = method(resolved);\n    else if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && Number.isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useToString(value) {\n  return computed(() => `${toValue$1(value)}`);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = shallowRef(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = toValue$1(truthyValue);\n      _value.value = _value.value === truthy ? toValue$1(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...typeof source === \"function\" ? source() : Array.isArray(source) ? source : toValue$1(source)];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = Array.from({ length: oldList.length });\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nfunction watchAtMost(source, cb, options) {\n  const {\n    count,\n    ...watchOptions\n  } = options;\n  const current = shallowRef(0);\n  const stop = watchWithFilter(\n    source,\n    (...args) => {\n      current.value += 1;\n      if (current.value >= toValue$1(count))\n        nextTick(() => stop());\n      cb(...args);\n    },\n    watchOptions\n  );\n  return { count: current, stop };\n}\n\nfunction watchDebounced(source, cb, options = {}) {\n  const {\n    debounce = 0,\n    maxWait = void 0,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: debounceFilter(debounce, { maxWait })\n    }\n  );\n}\n\nfunction watchDeep(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      deep: true\n    }\n  );\n}\n\nfunction watchIgnorable(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  const filteredCb = createFilterWrapper(\n    eventFilter,\n    cb\n  );\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    let ignore = false;\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore = true;\n      updater();\n      ignore = false;\n    };\n    stop = watch(\n      source,\n      (...args) => {\n        if (!ignore)\n          filteredCb(...args);\n      },\n      watchOptions\n    );\n  } else {\n    const disposables = [];\n    let ignoreCounter = 0;\n    let syncCounter = 0;\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter = syncCounter;\n    };\n    disposables.push(\n      watch(\n        source,\n        () => {\n          syncCounter++;\n        },\n        { ...watchOptions, flush: \"sync\" }\n      )\n    );\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter;\n      updater();\n      ignoreCounter += syncCounter - syncCounterPrev;\n    };\n    disposables.push(\n      watch(\n        source,\n        (...args) => {\n          const ignore = ignoreCounter > 0 && ignoreCounter === syncCounter;\n          ignoreCounter = 0;\n          syncCounter = 0;\n          if (ignore)\n            return;\n          filteredCb(...args);\n        },\n        watchOptions\n      )\n    );\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchImmediate(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      immediate: true\n    }\n  );\n}\n\nfunction watchOnce(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      once: true\n    }\n  );\n}\n\nfunction watchThrottled(source, cb, options = {}) {\n  const {\n    throttle = 0,\n    trailing = true,\n    leading = true,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: throttleFilter(throttle, trailing, leading)\n    }\n  );\n}\n\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return {\n    ...res,\n    trigger\n  };\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => toValue$1(item));\n  return toValue$1(sources);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  const stop = watch(\n    source,\n    (v, ov, onInvalidate) => {\n      if (v) {\n        if (options == null ? void 0 : options.once)\n          nextTick(() => stop());\n        cb(v, ov, onInvalidate);\n      }\n    },\n    {\n      ...options,\n      once: false\n    }\n  );\n  return stop;\n}\n\nexport { assert, refAutoReset as autoResetRef, bypassFilter, camelize, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createRef, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, computedEager as eagerComputed, extendRef, formatDate, get, getLifeCycleTarget, hasOwn, hyphenate, identity, watchIgnorable as ignorableWatch, increaseWithUnit, injectLocal, invoke, isClient, isDef, isDefined, isIOS, isObject, isWorker, makeDestructurable, noop, normalizeDate, notNullish, now, objectEntries, objectOmit, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, provideLocal, pxValue, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toArray, toReactive, toRef, toRefs, toValue, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchDeep, watchIgnorable, watchImmediate, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, makeDestructurable, camelize, isClient, toArray, watchImmediate, isObject, tryOnScopeDispose, isIOS, notNullish, tryOnMounted, objectOmit, promiseTimeout, until, injectLocal, provideLocal, pxValue, increaseWithUnit, objectEntries, createRef, createSingletonPromise, useTimeoutFn, pausableWatch, toRef, createEventHook, useIntervalFn, computedWithControl, timestamp, pausableFilter, watchIgnorable, debounceFilter, bypassFilter, createFilterWrapper, toRefs, watchOnce, containsProp, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, tryOnUnmounted, clamp, syncRef, objectPick, watchWithFilter, identity, isDef, whenever, isWorker } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, shallowRef, ref, watchEffect, computed, inject, defineComponent, h, TransitionGroup, Fragment, shallowReactive, toValue, unref, getCurrentInstance, onMounted, watch, customRef, onUpdated, readonly, reactive, hasInjectionContext, toRaw, shallowReadonly, nextTick, markRaw, getCurrentScope, isReadonly, onBeforeUpdate } from 'vue';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  var _a;\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    flush = \"pre\",\n    evaluating = void 0,\n    shallow = true,\n    onError = (_a = globalThis.reportError) != null ? _a : noop\n  } = options;\n  const started = shallowRef(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  }, { flush });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((oldValue) => options(source, oldValue));\n  } else {\n    return computed({\n      get: (oldValue) => options.get(source, oldValue),\n      set: options.set\n    });\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createReusableTemplate(options = {}) {\n  const {\n    inheritAttrs = true\n  } = options;\n  const render = shallowRef();\n  const define = /*@__PURE__*/ defineComponent({\n    setup(_, { slots }) {\n      return () => {\n        render.value = slots.default;\n      };\n    }\n  });\n  const reuse = /*@__PURE__*/ defineComponent({\n    inheritAttrs,\n    props: options.props,\n    setup(props, { attrs, slots }) {\n      return () => {\n        var _a;\n        if (!render.value && process.env.NODE_ENV !== \"production\")\n          throw new Error(\"[VueUse] Failed to find the definition of reusable template\");\n        const vnode = (_a = render.value) == null ? void 0 : _a.call(render, {\n          ...options.props == null ? keysToCamelKebabCase(attrs) : props,\n          $slots: slots\n        });\n        return inheritAttrs && (vnode == null ? void 0 : vnode.length) === 1 ? vnode[0] : vnode;\n      };\n    }\n  });\n  return makeDestructurable(\n    { define, reuse },\n    [define, reuse]\n  );\n}\nfunction keysToCamelKebabCase(obj) {\n  const newObj = {};\n  for (const key in obj)\n    newObj[camelize(key)] = obj[key];\n  return newObj;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createTemplatePromise(options = {}) {\n  let index = 0;\n  const instances = ref([]);\n  function create(...args) {\n    const props = shallowReactive({\n      key: index++,\n      args,\n      promise: void 0,\n      resolve: () => {\n      },\n      reject: () => {\n      },\n      isResolving: false,\n      options\n    });\n    instances.value.push(props);\n    props.promise = new Promise((_resolve, _reject) => {\n      props.resolve = (v) => {\n        props.isResolving = true;\n        return _resolve(v);\n      };\n      props.reject = _reject;\n    }).finally(() => {\n      props.promise = void 0;\n      const index2 = instances.value.indexOf(props);\n      if (index2 !== -1)\n        instances.value.splice(index2, 1);\n    });\n    return props.promise;\n  }\n  function start(...args) {\n    if (options.singleton && instances.value.length > 0)\n      return instances.value[0].promise;\n    return create(...args);\n  }\n  const component = /*@__PURE__*/ defineComponent((_, { slots }) => {\n    const renderList = () => instances.value.map((props) => {\n      var _a;\n      return h(Fragment, { key: props.key }, (_a = slots.default) == null ? void 0 : _a.call(slots, props));\n    });\n    if (options.transition)\n      return () => h(TransitionGroup, options.transition, renderList);\n    return renderList;\n  });\n  component.start = start;\n  return component;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction createUnrefFn(fn) {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => toValue(i)));\n  };\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = toValue(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nfunction useEventListener(...args) {\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options) => {\n    el.addEventListener(event, listener, options);\n    return () => el.removeEventListener(event, listener, options);\n  };\n  const firstParamTargets = computed(() => {\n    const test = toArray(toValue(args[0])).filter((e) => e != null);\n    return test.every((e) => typeof e !== \"string\") ? test : void 0;\n  });\n  const stopWatch = watchImmediate(\n    () => {\n      var _a, _b;\n      return [\n        (_b = (_a = firstParamTargets.value) == null ? void 0 : _a.map((e) => unrefElement(e))) != null ? _b : [defaultWindow].filter((e) => e != null),\n        toArray(toValue(firstParamTargets.value ? args[1] : args[0])),\n        toArray(unref(firstParamTargets.value ? args[2] : args[1])),\n        // @ts-expect-error - TypeScript gets the correct types, but somehow still complains\n        toValue(firstParamTargets.value ? args[3] : args[2])\n      ];\n    },\n    ([raw_targets, raw_events, raw_listeners, raw_options]) => {\n      cleanup();\n      if (!(raw_targets == null ? void 0 : raw_targets.length) || !(raw_events == null ? void 0 : raw_events.length) || !(raw_listeners == null ? void 0 : raw_listeners.length))\n        return;\n      const optionsClone = isObject(raw_options) ? { ...raw_options } : raw_options;\n      cleanups.push(\n        ...raw_targets.flatMap(\n          (el) => raw_events.flatMap(\n            (event) => raw_listeners.map((listener) => register(el, event, listener, optionsClone))\n          )\n        )\n      );\n    },\n    { flush: \"post\" }\n  );\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(cleanup);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false, controls = false } = options;\n  if (!window) {\n    return controls ? { stop: noop, cancel: noop, trigger: noop } : noop;\n  }\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    const listenerOptions = { passive: true };\n    Array.from(window.document.body.children).forEach((el) => el.addEventListener(\"click\", noop, listenerOptions));\n    window.document.documentElement.addEventListener(\"click\", noop, listenerOptions);\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return toValue(ignore).some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  function hasMultipleRoots(target2) {\n    const vm = toValue(target2);\n    return vm && vm.$.subTree.shapeFlag === 16;\n  }\n  function checkMultipleRoots(target2, event) {\n    const vm = toValue(target2);\n    const children = vm.$.subTree && vm.$.subTree.children;\n    if (children == null || !Array.isArray(children))\n      return false;\n    return children.some((child) => child.el === event.target || event.composedPath().includes(child.el));\n  }\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (event.target == null)\n      return;\n    if (!(el instanceof Element) && hasMultipleRoots(target) && checkMultipleRoots(target, event))\n      return;\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (\"detail\" in event && event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  let isProcessingClick = false;\n  const cleanup = [\n    useEventListener(window, \"click\", (event) => {\n      if (!isProcessingClick) {\n        isProcessingClick = true;\n        setTimeout(() => {\n          isProcessingClick = false;\n        }, 0);\n        listener(event);\n      }\n    }, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      setTimeout(() => {\n        var _a;\n        const el = unrefElement(target);\n        if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement))) {\n          handler(event);\n        }\n      }, 0);\n    }, { passive: true })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  if (controls) {\n    return {\n      stop,\n      cancel: () => {\n        shouldListen = false;\n      },\n      trigger: (event) => {\n        shouldListen = true;\n        listener(event);\n        shouldListen = false;\n      }\n    };\n  }\n  return stop;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useMounted() {\n  const isMounted = shallowRef(false);\n  const instance = getCurrentInstance();\n  if (instance) {\n    onMounted(() => {\n      isMounted.value = true;\n    }, instance);\n  }\n  return isMounted;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useSupported(callback) {\n  const isMounted = useMounted();\n  return computed(() => {\n    isMounted.value;\n    return Boolean(callback());\n  });\n}\n\nfunction useMutationObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...mutationOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const value = toValue(target);\n    const items = toArray(value).map(unrefElement).filter(notNullish);\n    return new Set(items);\n  });\n  const stopWatch = watch(\n    targets,\n    (newTargets) => {\n      cleanup();\n      if (isSupported.value && newTargets.size) {\n        observer = new MutationObserver(callback);\n        newTargets.forEach((el) => observer.observe(el, mutationOptions));\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const takeRecords = () => {\n    return observer == null ? void 0 : observer.takeRecords();\n  };\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop,\n    takeRecords\n  };\n}\n\nfunction onElementRemoval(target, callback, options = {}) {\n  const {\n    window = defaultWindow,\n    document = window == null ? void 0 : window.document,\n    flush = \"sync\"\n  } = options;\n  if (!window || !document)\n    return noop;\n  let stopFn;\n  const cleanupAndUpdate = (fn) => {\n    stopFn == null ? void 0 : stopFn();\n    stopFn = fn;\n  };\n  const stopWatch = watchEffect(() => {\n    const el = unrefElement(target);\n    if (el) {\n      const { stop } = useMutationObserver(\n        document,\n        (mutationsList) => {\n          const targetRemoved = mutationsList.map((mutation) => [...mutation.removedNodes]).flat().some((node) => node === el || node.contains(el));\n          if (targetRemoved) {\n            callback(mutationsList);\n          }\n        },\n        {\n          window,\n          childList: true,\n          subtree: true\n        }\n      );\n      cleanupAndUpdate(stop);\n    }\n  }, { flush });\n  const stopHandle = () => {\n    stopWatch();\n    cleanupAndUpdate();\n  };\n  tryOnScopeDispose(stopHandle);\n  return stopHandle;\n}\n\nfunction createKeyPredicate(keyFilter) {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n}\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const {\n    target = defaultWindow,\n    eventName = \"keydown\",\n    passive = false,\n    dedupe = false\n  } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (e.repeat && toValue(dedupe))\n      return;\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keydown\" });\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keypress\" });\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keyup\" });\n}\n\nconst DEFAULT_DELAY = 500;\nconst DEFAULT_THRESHOLD = 10;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  let posStart;\n  let startTimestamp;\n  let hasLongPressed = false;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n    posStart = void 0;\n    startTimestamp = void 0;\n    hasLongPressed = false;\n  }\n  function onRelease(ev) {\n    var _a2, _b2, _c;\n    const [_startTimestamp, _posStart, _hasLongPressed] = [startTimestamp, posStart, hasLongPressed];\n    clear();\n    if (!(options == null ? void 0 : options.onMouseUp) || !_posStart || !_startTimestamp)\n      return;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - _posStart.x;\n    const dy = ev.y - _posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    options.onMouseUp(ev.timeStamp - _startTimestamp, distance, _hasLongPressed);\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    posStart = {\n      x: ev.x,\n      y: ev.y\n    };\n    startTimestamp = ev.timeStamp;\n    timeout = setTimeout(\n      () => {\n        hasLongPressed = true;\n        handler(ev);\n      },\n      (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY\n    );\n  }\n  function onMove(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if (!posStart || (options == null ? void 0 : options.distanceThreshold) === false)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - posStart.x;\n    const dy = ev.y - posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    if (distance >= ((_d = options == null ? void 0 : options.distanceThreshold) != null ? _d : DEFAULT_THRESHOLD))\n      clear();\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  const cleanup = [\n    useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions),\n    useEventListener(elementRef, \"pointermove\", onMove, listenerOptions),\n    useEventListener(elementRef, [\"pointerup\", \"pointerleave\"], onRelease, listenerOptions)\n  ];\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nfunction isFocusedElementEditable() {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n}\nfunction isTypedCharValid({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n}\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    if (!isFocusedElementEditable() && isTypedCharValid(event)) {\n      callback(event);\n    }\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useActiveElement(options = {}) {\n  var _a;\n  const {\n    window = defaultWindow,\n    deep = true,\n    triggerOnRemoval = false\n  } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const getDeepActiveElement = () => {\n    var _a2;\n    let element = document == null ? void 0 : document.activeElement;\n    if (deep) {\n      while (element == null ? void 0 : element.shadowRoot)\n        element = (_a2 = element == null ? void 0 : element.shadowRoot) == null ? void 0 : _a2.activeElement;\n    }\n    return element;\n  };\n  const activeElement = shallowRef();\n  const trigger = () => {\n    activeElement.value = getDeepActiveElement();\n  };\n  if (window) {\n    const listenerOptions = {\n      capture: true,\n      passive: true\n    };\n    useEventListener(\n      window,\n      \"blur\",\n      (event) => {\n        if (event.relatedTarget !== null)\n          return;\n        trigger();\n      },\n      listenerOptions\n    );\n    useEventListener(\n      window,\n      \"focus\",\n      trigger,\n      listenerOptions\n    );\n  }\n  if (triggerOnRemoval) {\n    onElementRemoval(activeElement, trigger, { document });\n  }\n  trigger();\n  return activeElement;\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    fpsLimit = void 0,\n    window = defaultWindow,\n    once = false\n  } = options;\n  const isActive = shallowRef(false);\n  const intervalLimit = computed(() => {\n    return fpsLimit ? 1e3 / toValue(fpsLimit) : null;\n  });\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    if (!previousFrameTimestamp)\n      previousFrameTimestamp = timestamp;\n    const delta = timestamp - previousFrameTimestamp;\n    if (intervalLimit.value && delta < intervalLimit.value) {\n      rafId = window.requestAnimationFrame(loop);\n      return;\n    }\n    previousFrameTimestamp = timestamp;\n    fn({ delta, timestamp });\n    if (once) {\n      isActive.value = false;\n      rafId = null;\n      return;\n    }\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      previousFrameTimestamp = 0;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useAnimate(target, keyframes, options) {\n  let config;\n  let animateOptions;\n  if (isObject(options)) {\n    config = options;\n    animateOptions = objectOmit(options, [\"window\", \"immediate\", \"commitStyles\", \"persist\", \"onReady\", \"onError\"]);\n  } else {\n    config = { duration: options };\n    animateOptions = options;\n  }\n  const {\n    window = defaultWindow,\n    immediate = true,\n    commitStyles,\n    persist,\n    playbackRate: _playbackRate = 1,\n    onReady,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = config;\n  const isSupported = useSupported(() => window && HTMLElement && \"animate\" in HTMLElement.prototype);\n  const animate = shallowRef(void 0);\n  const store = shallowReactive({\n    startTime: null,\n    currentTime: null,\n    timeline: null,\n    playbackRate: _playbackRate,\n    pending: false,\n    playState: immediate ? \"idle\" : \"paused\",\n    replaceState: \"active\"\n  });\n  const pending = computed(() => store.pending);\n  const playState = computed(() => store.playState);\n  const replaceState = computed(() => store.replaceState);\n  const startTime = computed({\n    get() {\n      return store.startTime;\n    },\n    set(value) {\n      store.startTime = value;\n      if (animate.value)\n        animate.value.startTime = value;\n    }\n  });\n  const currentTime = computed({\n    get() {\n      return store.currentTime;\n    },\n    set(value) {\n      store.currentTime = value;\n      if (animate.value) {\n        animate.value.currentTime = value;\n        syncResume();\n      }\n    }\n  });\n  const timeline = computed({\n    get() {\n      return store.timeline;\n    },\n    set(value) {\n      store.timeline = value;\n      if (animate.value)\n        animate.value.timeline = value;\n    }\n  });\n  const playbackRate = computed({\n    get() {\n      return store.playbackRate;\n    },\n    set(value) {\n      store.playbackRate = value;\n      if (animate.value)\n        animate.value.playbackRate = value;\n    }\n  });\n  const play = () => {\n    if (animate.value) {\n      try {\n        animate.value.play();\n        syncResume();\n      } catch (e) {\n        syncPause();\n        onError(e);\n      }\n    } else {\n      update();\n    }\n  };\n  const pause = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.pause();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const reverse = () => {\n    var _a;\n    if (!animate.value)\n      update();\n    try {\n      (_a = animate.value) == null ? void 0 : _a.reverse();\n      syncResume();\n    } catch (e) {\n      syncPause();\n      onError(e);\n    }\n  };\n  const finish = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.finish();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const cancel = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.cancel();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  watch(() => unrefElement(target), (el) => {\n    if (el) {\n      update(true);\n    } else {\n      animate.value = void 0;\n    }\n  });\n  watch(() => keyframes, (value) => {\n    if (animate.value) {\n      update();\n      const targetEl = unrefElement(target);\n      if (targetEl) {\n        animate.value.effect = new KeyframeEffect(\n          targetEl,\n          toValue(value),\n          animateOptions\n        );\n      }\n    }\n  }, { deep: true });\n  tryOnMounted(() => update(true), false);\n  tryOnScopeDispose(cancel);\n  function update(init) {\n    const el = unrefElement(target);\n    if (!isSupported.value || !el)\n      return;\n    if (!animate.value)\n      animate.value = el.animate(toValue(keyframes), animateOptions);\n    if (persist)\n      animate.value.persist();\n    if (_playbackRate !== 1)\n      animate.value.playbackRate = _playbackRate;\n    if (init && !immediate)\n      animate.value.pause();\n    else\n      syncResume();\n    onReady == null ? void 0 : onReady(animate.value);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(animate, [\"cancel\", \"finish\", \"remove\"], syncPause, listenerOptions);\n  useEventListener(animate, \"finish\", () => {\n    var _a;\n    if (commitStyles)\n      (_a = animate.value) == null ? void 0 : _a.commitStyles();\n  }, listenerOptions);\n  const { resume: resumeRef, pause: pauseRef } = useRafFn(() => {\n    if (!animate.value)\n      return;\n    store.pending = animate.value.pending;\n    store.playState = animate.value.playState;\n    store.replaceState = animate.value.replaceState;\n    store.startTime = animate.value.startTime;\n    store.currentTime = animate.value.currentTime;\n    store.timeline = animate.value.timeline;\n    store.playbackRate = animate.value.playbackRate;\n  }, { immediate: false });\n  function syncResume() {\n    if (isSupported.value)\n      resumeRef();\n  }\n  function syncPause() {\n    if (isSupported.value && window)\n      window.requestAnimationFrame(pauseRef);\n  }\n  return {\n    isSupported,\n    animate,\n    // actions\n    play,\n    pause,\n    reverse,\n    finish,\n    cancel,\n    // state\n    pending,\n    playState,\n    replaceState,\n    startTime,\n    currentTime,\n    timeline,\n    playbackRate\n  };\n}\n\nfunction useAsyncQueue(tasks, options) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop,\n    signal\n  } = options || {};\n  const promiseState = {\n    aborted: \"aborted\",\n    fulfilled: \"fulfilled\",\n    pending: \"pending\",\n    rejected: \"rejected\"\n  };\n  const initialResult = Array.from(Array.from({ length: tasks.length }), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = shallowRef(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, new Error(\"aborted\"));\n        return;\n      }\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      const done = curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        if (activeIndex.value === tasks.length - 1)\n          onFinished();\n        return currentRes;\n      });\n      if (!signal)\n        return done;\n      return Promise.race([done, whenAborted(signal)]);\n    }).catch((e) => {\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, e);\n        return e;\n      }\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\nfunction whenAborted(signal) {\n  return new Promise((resolve, reject) => {\n    const error = new Error(\"aborted\");\n    if (signal.aborted)\n      reject(error);\n    else\n      signal.addEventListener(\"abort\", () => reject(error), { once: true });\n  });\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  var _a;\n  const {\n    immediate = true,\n    delay = 0,\n    onError = (_a = globalThis.reportError) != null ? _a : noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = shallowRef(false);\n  const isLoading = shallowRef(false);\n  const error = shallowRef(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw e;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate) {\n    execute(delay);\n  }\n  const shell = {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute,\n    executeImmediate: (...args) => execute(0, ...args)\n  };\n  function waitUntilIsLoaded() {\n    return new Promise((resolve, reject) => {\n      until(isLoading).toBe(false).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilIsLoaded().then(onFulfilled, onRejected);\n    }\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = shallowRef(\"\");\n  const promise = shallowRef();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = toValue(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => {\n      base64.value = (options == null ? void 0 : options.dataUrl) === false ? res.replace(/^data:.*?;base64,/, \"\") : res;\n    });\n    return promise.value;\n  }\n  if (isRef(target) || typeof target === \"function\")\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useBattery(options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator && typeof navigator.getBattery === \"function\");\n  const charging = shallowRef(false);\n  const chargingTime = shallowRef(0);\n  const dischargingTime = shallowRef(0);\n  const level = shallowRef(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      useEventListener(battery, events, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef();\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = shallowRef();\n  const isConnected = shallowRef(false);\n  function reset() {\n    isConnected.value = false;\n    device.value = void 0;\n    server.value = void 0;\n  }\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      useEventListener(device, \"gattserverdisconnected\", reset, { passive: true });\n      try {\n        server.value = await device.value.gatt.connect();\n        isConnected.value = server.value.connected;\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected: readonly(isConnected),\n    // Device:\n    device,\n    requestDevice,\n    // Server:\n    server,\n    // Errors:\n    error\n  };\n}\n\nconst ssrWidthSymbol = Symbol(\"vueuse-ssr-width\");\n// @__NO_SIDE_EFFECTS__\nfunction useSSRWidth() {\n  const ssrWidth = hasInjectionContext() ? injectLocal(ssrWidthSymbol, null) : null;\n  return typeof ssrWidth === \"number\" ? ssrWidth : void 0;\n}\nfunction provideSSRWidth(width, app) {\n  if (app !== void 0) {\n    app.provide(ssrWidthSymbol, width);\n  } else {\n    provideLocal(ssrWidthSymbol, width);\n  }\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow, ssrWidth = useSSRWidth() } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  const ssrSupport = shallowRef(typeof ssrWidth === \"number\");\n  const mediaQuery = shallowRef();\n  const matches = shallowRef(false);\n  const handler = (event) => {\n    matches.value = event.matches;\n  };\n  watchEffect(() => {\n    if (ssrSupport.value) {\n      ssrSupport.value = !isSupported.value;\n      const queryStrings = toValue(query).split(\",\");\n      matches.value = queryStrings.some((queryString) => {\n        const not = queryString.includes(\"not all\");\n        const minWidth = queryString.match(/\\(\\s*min-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        const maxWidth = queryString.match(/\\(\\s*max-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        let res = Boolean(minWidth || maxWidth);\n        if (minWidth && res) {\n          res = ssrWidth >= pxValue(minWidth[1]);\n        }\n        if (maxWidth && res) {\n          res = ssrWidth <= pxValue(maxWidth[1]);\n        }\n        return not ? !res : res;\n      });\n      return;\n    }\n    if (!isSupported.value)\n      return;\n    mediaQuery.value = window.matchMedia(toValue(query));\n    matches.value = mediaQuery.value.matches;\n  });\n  useEventListener(mediaQuery, \"change\", handler, { passive: true });\n  return computed(() => matches.value);\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetifyV2 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1264,\n  xl: 1904\n};\nconst breakpointsVuetifyV3 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1280,\n  xl: 1920,\n  xxl: 2560\n};\nconst breakpointsVuetify = breakpointsVuetifyV2;\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 0,\n  sm: 600,\n  md: 1024,\n  lg: 1440,\n  xl: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\nconst breakpointsPrimeFlex = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nconst breakpointsElement = {\n  xs: 0,\n  sm: 768,\n  md: 992,\n  lg: 1200,\n  xl: 1920\n};\n\n// @__NO_SIDE_EFFECTS__\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = toValue(breakpoints[toValue(k)]);\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow, strategy = \"min-width\", ssrWidth = useSSRWidth() } = options;\n  const ssrSupport = typeof ssrWidth === \"number\";\n  const mounted = ssrSupport ? shallowRef(false) : { value: true };\n  if (ssrSupport) {\n    tryOnMounted(() => mounted.value = !!window);\n  }\n  function match(query, size) {\n    if (!mounted.value && ssrSupport) {\n      return query === \"min\" ? ssrWidth >= pxValue(size) : ssrWidth <= pxValue(size);\n    }\n    if (!window)\n      return false;\n    return window.matchMedia(`(${query}-width: ${size})`).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(() => `(min-width: ${getValue(k)})`, options);\n  };\n  const smallerOrEqual = (k) => {\n    return useMediaQuery(() => `(max-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => strategy === \"min-width\" ? greaterOrEqual(k) : smallerOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  function current() {\n    const points = Object.keys(breakpoints).map((k) => [k, shortcutMethods[k], pxValue(getValue(k))]).sort((a, b) => a[2] - b[2]);\n    return computed(() => points.filter(([, v]) => v.value).map(([k]) => k));\n  }\n  return Object.assign(shortcutMethods, {\n    greaterOrEqual,\n    smallerOrEqual,\n    greater(k) {\n      return useMediaQuery(() => `(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    smaller(k) {\n      return useMediaQuery(() => `(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(() => `(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(\"min\", getValue(k, 0.1));\n    },\n    isGreaterOrEqual(k) {\n      return match(\"min\", getValue(k));\n    },\n    isSmaller(k) {\n      return match(\"max\", getValue(k, -0.1));\n    },\n    isSmallerOrEqual(k) {\n      return match(\"max\", getValue(k));\n    },\n    isInBetween(a, b) {\n      return match(\"min\", getValue(a)) && match(\"max\", getValue(b, -0.1));\n    },\n    current,\n    active() {\n      const bps = current();\n      return computed(() => bps.value.length === 0 ? \"\" : bps.value.at(strategy === \"min-width\" ? -1 : 0));\n    }\n  });\n}\n\nfunction useBroadcastChannel(options) {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = shallowRef(false);\n  const channel = ref();\n  const data = ref();\n  const error = shallowRef(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      const listenerOptions = {\n        passive: true\n      };\n      useEventListener(channel, \"message\", (e) => {\n        data.value = e.data;\n      }, listenerOptions);\n      useEventListener(channel, \"messageerror\", (e) => {\n        error.value = e;\n      }, listenerOptions);\n      useEventListener(channel, \"close\", () => {\n        isClosed.value = true;\n      }, listenerOptions);\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n}\n\nconst WRITABLE_PROPERTIES = [\n  \"hash\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"search\"\n];\n// @__NO_SIDE_EFFECTS__\nfunction useBrowserLocation(options = {}) {\n  const { window = defaultWindow } = options;\n  const refs = Object.fromEntries(\n    WRITABLE_PROPERTIES.map((key) => [key, ref()])\n  );\n  for (const [key, ref] of objectEntries(refs)) {\n    watch(ref, (value) => {\n      if (!(window == null ? void 0 : window.location) || window.location[key] === value)\n        return;\n      window.location[key] = value;\n    });\n  }\n  const buildState = (trigger) => {\n    var _a;\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { origin } = (window == null ? void 0 : window.location) || {};\n    for (const key of WRITABLE_PROPERTIES)\n      refs[key].value = (_a = window == null ? void 0 : window.location) == null ? void 0 : _a[key];\n    return reactive({\n      trigger,\n      state: state2,\n      length,\n      origin,\n      ...refs\n    });\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), listenerOptions);\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), listenerOptions);\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, options) {\n  const { deepRefs = true, ...watchOptions } = options || {};\n  const cachedValue = createRef(refValue.value, deepRefs);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  const permissionStatus = shallowRef();\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = shallowRef();\n  const update = () => {\n    var _a, _b;\n    state.value = (_b = (_a = permissionStatus.value) == null ? void 0 : _a.state) != null ? _b : \"prompt\";\n  };\n  useEventListener(permissionStatus, \"change\", update, { passive: true });\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus.value) {\n      try {\n        permissionStatus.value = await navigator.permissions.query(desc);\n      } catch (e) {\n        permissionStatus.value = void 0;\n      } finally {\n        update();\n      }\n    }\n    if (controls)\n      return toRaw(permissionStatus.value);\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const permissionRead = usePermission(\"clipboard-read\");\n  const permissionWrite = usePermission(\"clipboard-write\");\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = shallowRef(\"\");\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  async function updateText() {\n    let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionRead.value));\n    if (!useLegacy) {\n      try {\n        text.value = await navigator.clipboard.readText();\n      } catch (e) {\n        useLegacy = true;\n      }\n    }\n    if (useLegacy) {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateText, { passive: true });\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionWrite.value));\n      if (!useLegacy) {\n        try {\n          await navigator.clipboard.writeText(value);\n        } catch (e) {\n          useLegacy = true;\n        }\n      }\n      if (useLegacy)\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  function isAllowed(status) {\n    return status === \"granted\" || status === \"prompt\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nfunction useClipboardItems(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500\n  } = options;\n  const isSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const content = ref([]);\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  function updateContent() {\n    if (isSupported.value) {\n      navigator.clipboard.read().then((items) => {\n        content.value = items;\n      });\n    }\n  }\n  if (isSupported.value && read) {\n    useEventListener([\"copy\", \"cut\"], updateContent, { passive: true });\n  }\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      await navigator.clipboard.write(value);\n      content.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  return {\n    isSupported,\n    content: shallowReadonly(content),\n    copied: readonly(copied),\n    copy,\n    read: updateContent\n  };\n}\n\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const isModified = shallowRef(false);\n  let _lastSync = false;\n  const {\n    manual,\n    clone = cloneFnJSON,\n    // watch options\n    deep = true,\n    immediate = true\n  } = options;\n  watch(cloned, () => {\n    if (_lastSync) {\n      _lastSync = false;\n      return;\n    }\n    isModified.value = true;\n  }, {\n    deep: true,\n    flush: \"sync\"\n  });\n  function sync() {\n    _lastSync = true;\n    isModified.value = false;\n    cloned.value = clone(toValue(source));\n  }\n  if (!manual && (isRef(source) || typeof source === \"function\")) {\n    watch(source, sync, {\n      ...options,\n      deep,\n      immediate\n    });\n  } else {\n    sync();\n  }\n  return { cloned, isModified, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\nconst handlers = /* @__PURE__ */ getHandlers();\nfunction getHandlers() {\n  if (!(globalKey in _global))\n    _global[globalKey] = _global[globalKey] || {};\n  return _global[globalKey];\n}\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    initOnMounted\n  } = options;\n  const data = (shallow ? shallowRef : ref)(typeof defaults === \"function\" ? defaults() : defaults);\n  const keyComputed = computed(() => toValue(key));\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = toValue(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(\n    data,\n    (newValue) => write(newValue),\n    { flush, deep, eventFilter }\n  );\n  watch(keyComputed, () => update(), { flush });\n  let firstMounted = false;\n  const onStorageEvent = (ev) => {\n    if (initOnMounted && !firstMounted) {\n      return;\n    }\n    update(ev);\n  };\n  const onStorageCustomEvent = (ev) => {\n    if (initOnMounted && !firstMounted) {\n      return;\n    }\n    updateFromCustomEvent(ev);\n  };\n  if (window && listenToStorageChanges) {\n    if (storage instanceof Storage)\n      useEventListener(window, \"storage\", onStorageEvent, { passive: true });\n    else\n      useEventListener(window, customStorageEventName, onStorageCustomEvent);\n  }\n  if (initOnMounted) {\n    tryOnMounted(() => {\n      firstMounted = true;\n      update();\n    });\n  } else {\n    update();\n  }\n  function dispatchWriteEvent(oldValue, newValue) {\n    if (window) {\n      const payload = {\n        key: keyComputed.value,\n        oldValue,\n        newValue,\n        storageArea: storage\n      };\n      window.dispatchEvent(storage instanceof Storage ? new StorageEvent(\"storage\", payload) : new CustomEvent(customStorageEventName, {\n        detail: payload\n      }));\n    }\n  }\n  function write(v) {\n    try {\n      const oldValue = storage.getItem(keyComputed.value);\n      if (v == null) {\n        dispatchWriteEvent(oldValue, null);\n        storage.removeItem(keyComputed.value);\n      } else {\n        const serialized = serializer.write(v);\n        if (oldValue !== serialized) {\n          storage.setItem(keyComputed.value, serialized);\n          dispatchWriteEvent(oldValue, serialized);\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(keyComputed.value);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit != null)\n        storage.setItem(keyComputed.value, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (typeof mergeDefaults === \"function\")\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return { ...rawInit, ...value };\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== keyComputed.value) {\n      return;\n    }\n    pauseWatch();\n    try {\n      const serializedData = serializer.write(data.value);\n      if (event === void 0 || (event == null ? void 0 : event.newValue) !== serializedData) {\n        data.value = read(event);\n      }\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  return data;\n}\n\nconst CSS_DISABLE_TRANS = \"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\";\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto,\n    disableTransition = true\n  } = options;\n  const modes = {\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\",\n    ...options.modes || {}\n  };\n  const preferredDark = usePreferredDark({ window });\n  const system = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? toRef(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed(() => store.value === \"auto\" ? system.value : store.value);\n  const updateHTMLAttrs = getSSRHandler(\n    \"updateHTMLAttrs\",\n    (selector2, attribute2, value) => {\n      const el = typeof selector2 === \"string\" ? window == null ? void 0 : window.document.querySelector(selector2) : unrefElement(selector2);\n      if (!el)\n        return;\n      const classesToAdd = /* @__PURE__ */ new Set();\n      const classesToRemove = /* @__PURE__ */ new Set();\n      let attributeToChange = null;\n      if (attribute2 === \"class\") {\n        const current = value.split(/\\s/g);\n        Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n          if (current.includes(v))\n            classesToAdd.add(v);\n          else\n            classesToRemove.add(v);\n        });\n      } else {\n        attributeToChange = { key: attribute2, value };\n      }\n      if (classesToAdd.size === 0 && classesToRemove.size === 0 && attributeToChange === null)\n        return;\n      let style;\n      if (disableTransition) {\n        style = window.document.createElement(\"style\");\n        style.appendChild(document.createTextNode(CSS_DISABLE_TRANS));\n        window.document.head.appendChild(style);\n      }\n      for (const c of classesToAdd) {\n        el.classList.add(c);\n      }\n      for (const c of classesToRemove) {\n        el.classList.remove(c);\n      }\n      if (attributeToChange) {\n        el.setAttribute(attributeToChange.key, attributeToChange.value);\n      }\n      if (disableTransition) {\n        window.getComputedStyle(style).opacity;\n        document.head.removeChild(style);\n      }\n    }\n  );\n  function defaultOnChanged(mode) {\n    var _a;\n    updateHTMLAttrs(selector, attribute, (_a = modes[mode]) != null ? _a : mode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  const auto = computed({\n    get() {\n      return emitAuto ? store.value : state.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  return Object.assign(auto, { store, system, state });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useConfirmDialog(revealed = shallowRef(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCountdown(initialCountdown, options) {\n  var _a, _b;\n  const remaining = shallowRef(toValue(initialCountdown));\n  const intervalController = useIntervalFn(() => {\n    var _a2, _b2;\n    const value = remaining.value - 1;\n    remaining.value = value < 0 ? 0 : value;\n    (_a2 = options == null ? void 0 : options.onTick) == null ? void 0 : _a2.call(options);\n    if (remaining.value <= 0) {\n      intervalController.pause();\n      (_b2 = options == null ? void 0 : options.onComplete) == null ? void 0 : _b2.call(options);\n    }\n  }, (_a = options == null ? void 0 : options.interval) != null ? _a : 1e3, { immediate: (_b = options == null ? void 0 : options.immediate) != null ? _b : false });\n  const reset = (countdown) => {\n    var _a2;\n    remaining.value = (_a2 = toValue(countdown)) != null ? _a2 : toValue(initialCountdown);\n  };\n  const stop = () => {\n    intervalController.pause();\n    reset();\n  };\n  const resume = () => {\n    if (!intervalController.isActive.value) {\n      if (remaining.value > 0) {\n        intervalController.resume();\n      }\n    }\n  };\n  const start = (countdown) => {\n    reset(countdown);\n    intervalController.resume();\n  };\n  return {\n    remaining,\n    reset,\n    stop,\n    start,\n    pause: intervalController.pause,\n    resume,\n    isActive: intervalController.isActive\n  };\n}\n\nfunction useCssVar(prop, target, options = {}) {\n  const { window = defaultWindow, initialValue, observe = false } = options;\n  const variable = shallowRef(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  function updateCssVar() {\n    var _a;\n    const key = toValue(prop);\n    const el = toValue(elRef);\n    if (el && window && key) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(key)) == null ? void 0 : _a.trim();\n      variable.value = value || variable.value || initialValue;\n    }\n  }\n  if (observe) {\n    useMutationObserver(elRef, updateCssVar, {\n      attributeFilter: [\"style\", \"class\"],\n      window\n    });\n  }\n  watch(\n    [elRef, () => toValue(prop)],\n    (_, old) => {\n      if (old[0] && old[1])\n        old[0].style.removeProperty(old[1]);\n      updateCssVar();\n    },\n    { immediate: true }\n  );\n  watch(\n    [variable, elRef],\n    ([val, el]) => {\n      const raw_prop = toValue(prop);\n      if ((el == null ? void 0 : el.style) && raw_prop) {\n        if (val == null)\n          el.style.removeProperty(raw_prop);\n        else\n          el.style.setProperty(raw_prop, val);\n      }\n    },\n    { immediate: true }\n  );\n  return variable;\n}\n\nfunction useCurrentElement(rootComponent) {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(\n    () => null,\n    () => rootComponent ? unrefElement(rootComponent) : vm.proxy.$el\n  );\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  const state = shallowRef(getInitialValue());\n  const listRef = toRef(list);\n  const index = computed({\n    get() {\n      var _a;\n      const targetList = listRef.value;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, targetList) : targetList.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a = options == null ? void 0 : options.fallbackIndex) != null ? _a : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const targetList = listRef.value;\n    const length = targetList.length;\n    const index2 = (i % length + length) % length;\n    const value = targetList[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  function getInitialValue() {\n    var _a, _b;\n    return (_b = toValue((_a = options == null ? void 0 : options.initialValue) != null ? _a : toValue(list)[0])) != null ? _b : void 0;\n  }\n  watch(listRef, () => set(index.value));\n  return {\n    state,\n    index,\n    next,\n    prev,\n    go: set\n  };\n}\n\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\"\n  } = options;\n  const mode = useColorMode({\n    ...options,\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\", defaultHandler, mode2);\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  });\n  const system = computed(() => mode.system.value);\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      const modeVal = v ? \"dark\" : \"light\";\n      if (system.value === modeVal)\n        mode.value = \"auto\";\n      else\n        mode.value = modeVal;\n    }\n  });\n  return isDark;\n}\n\nfunction fnBypass(v) {\n  return v;\n}\nfunction fnSetSource(source, value) {\n  return source.value = value;\n}\nfunction defaultDump(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Number.POSITIVE_INFINITY);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter,\n    shouldCommit = () => true\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  let lastRawValue = source.value;\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(\n    source,\n    commit,\n    { deep, flush, eventFilter: composedFilter }\n  );\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n      lastRawValue = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, { ...options, clone: options.clone || deep, setSource });\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    if (!shouldCommit(lastRawValue, source.value))\n      return;\n    lastRawValue = source.value;\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return {\n    ...manualHistory,\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  };\n}\n\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions = false,\n    eventFilter = bypassFilter\n  } = options;\n  const isSupported = useSupported(() => typeof DeviceMotionEvent !== \"undefined\");\n  const requirePermissions = useSupported(() => isSupported.value && \"requestPermission\" in DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === \"function\");\n  const permissionGranted = shallowRef(false);\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = shallowRef(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  function init() {\n    if (window) {\n      const onDeviceMotion = createFilterWrapper(\n        eventFilter,\n        (event) => {\n          var _a, _b, _c, _d, _e, _f, _g, _h, _i;\n          acceleration.value = {\n            x: ((_a = event.acceleration) == null ? void 0 : _a.x) || null,\n            y: ((_b = event.acceleration) == null ? void 0 : _b.y) || null,\n            z: ((_c = event.acceleration) == null ? void 0 : _c.z) || null\n          };\n          accelerationIncludingGravity.value = {\n            x: ((_d = event.accelerationIncludingGravity) == null ? void 0 : _d.x) || null,\n            y: ((_e = event.accelerationIncludingGravity) == null ? void 0 : _e.y) || null,\n            z: ((_f = event.accelerationIncludingGravity) == null ? void 0 : _f.z) || null\n          };\n          rotationRate.value = {\n            alpha: ((_g = event.rotationRate) == null ? void 0 : _g.alpha) || null,\n            beta: ((_h = event.rotationRate) == null ? void 0 : _h.beta) || null,\n            gamma: ((_i = event.rotationRate) == null ? void 0 : _i.gamma) || null\n          };\n          interval.value = event.interval;\n        }\n      );\n      useEventListener(window, \"devicemotion\", onDeviceMotion, { passive: true });\n    }\n  }\n  const ensurePermissions = async () => {\n    if (!requirePermissions.value)\n      permissionGranted.value = true;\n    if (permissionGranted.value)\n      return;\n    if (requirePermissions.value) {\n      const requestPermission = DeviceMotionEvent.requestPermission;\n      try {\n        const response = await requestPermission();\n        if (response === \"granted\") {\n          permissionGranted.value = true;\n          init();\n        }\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  if (isSupported.value) {\n    if (requestPermissions && requirePermissions.value) {\n      ensurePermissions().then(() => init());\n    } else {\n      init();\n    }\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval,\n    isSupported,\n    requirePermissions,\n    ensurePermissions,\n    permissionGranted\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = shallowRef(false);\n  const alpha = shallowRef(null);\n  const beta = shallowRef(null);\n  const gamma = shallowRef(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    }, { passive: true });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useDevicePixelRatio(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const pixelRatio = shallowRef(1);\n  const query = useMediaQuery(() => `(resolution: ${pixelRatio.value}dppx)`, options);\n  let stop = noop;\n  if (window) {\n    stop = watchImmediate(query, () => pixelRatio.value = window.devicePixelRatio);\n  }\n  return {\n    pixelRatio: readonly(pixelRatio),\n    stop\n  };\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = shallowRef(false);\n  let stream;\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n    if (stream) {\n      stream.getTracks().forEach((t) => t.stop());\n      stream = null;\n    }\n  }\n  async function ensurePermissions() {\n    const deviceName = constraints.video ? \"camera\" : \"microphone\";\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(deviceName, { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      let granted = true;\n      try {\n        const allDevices = await navigator.mediaDevices.enumerateDevices();\n        const hasCamera = allDevices.some((device) => device.kind === \"videoinput\");\n        const hasMicrophone = allDevices.some((device) => device.kind === \"audioinput\" || device.kind === \"audiooutput\");\n        constraints.video = hasCamera ? constraints.video : false;\n        constraints.audio = hasMicrophone ? constraints.audio : false;\n        stream = await navigator.mediaDevices.getUserMedia(constraints);\n      } catch (e) {\n        stream = null;\n        granted = false;\n      }\n      update();\n      permissionGranted.value = granted;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update, { passive: true });\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    var _a2;\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => useEventListener(t, \"ended\", stop, { passive: true }));\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else\n        _stop();\n    },\n    { immediate: true }\n  );\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useDocumentVisibility(options = {}) {\n  const { document = defaultDocument } = options;\n  if (!document)\n    return shallowRef(\"visible\");\n  const visibility = shallowRef(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  }, { passive: true });\n  return visibility;\n}\n\nfunction useDraggable(target, options = {}) {\n  var _a;\n  const {\n    pointerTypes,\n    preventDefault,\n    stopPropagation,\n    exact,\n    onMove,\n    onEnd,\n    onStart,\n    initialValue,\n    axis = \"both\",\n    draggingElement = defaultWindow,\n    containerElement,\n    handle: draggingHandle = target,\n    buttons = [0]\n  } = options;\n  const position = ref(\n    (_a = toValue(initialValue)) != null ? _a : { x: 0, y: 0 }\n  );\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (pointerTypes)\n      return pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (toValue(preventDefault))\n      e.preventDefault();\n    if (toValue(stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!toValue(buttons).includes(e.button))\n      return;\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (toValue(exact) && e.target !== toValue(target))\n      return;\n    const container = toValue(containerElement);\n    const containerRect = (_a2 = container == null ? void 0 : container.getBoundingClientRect) == null ? void 0 : _a2.call(container);\n    const targetRect = toValue(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - (container ? targetRect.left - containerRect.left + container.scrollLeft : targetRect.left),\n      y: e.clientY - (container ? targetRect.top - containerRect.top + container.scrollTop : targetRect.top)\n    };\n    if ((onStart == null ? void 0 : onStart(pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    const container = toValue(containerElement);\n    const targetRect = toValue(target).getBoundingClientRect();\n    let { x, y } = position.value;\n    if (axis === \"x\" || axis === \"both\") {\n      x = e.clientX - pressedDelta.value.x;\n      if (container)\n        x = Math.min(Math.max(0, x), container.scrollWidth - targetRect.width);\n    }\n    if (axis === \"y\" || axis === \"both\") {\n      y = e.clientY - pressedDelta.value.y;\n      if (container)\n        y = Math.min(Math.max(0, y), container.scrollHeight - targetRect.height);\n    }\n    position.value = {\n      x,\n      y\n    };\n    onMove == null ? void 0 : onMove(position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    onEnd == null ? void 0 : onEnd(position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    const config = () => {\n      var _a2;\n      return {\n        capture: (_a2 = options.capture) != null ? _a2 : true,\n        passive: !toValue(preventDefault)\n      };\n    };\n    useEventListener(draggingHandle, \"pointerdown\", start, config);\n    useEventListener(draggingElement, \"pointermove\", move, config);\n    useEventListener(draggingElement, \"pointerup\", end, config);\n  }\n  return {\n    ...toRefs(position),\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(\n      () => `left:${position.value.x}px;top:${position.value.y}px;`\n    )\n  };\n}\n\nfunction useDropZone(target, options = {}) {\n  var _a, _b;\n  const isOverDropZone = shallowRef(false);\n  const files = shallowRef(null);\n  let counter = 0;\n  let isValid = true;\n  if (isClient) {\n    const _options = typeof options === \"function\" ? { onDrop: options } : options;\n    const multiple = (_a = _options.multiple) != null ? _a : true;\n    const preventDefaultForUnhandled = (_b = _options.preventDefaultForUnhandled) != null ? _b : false;\n    const getFiles = (event) => {\n      var _a2, _b2;\n      const list = Array.from((_b2 = (_a2 = event.dataTransfer) == null ? void 0 : _a2.files) != null ? _b2 : []);\n      return list.length === 0 ? null : multiple ? list : [list[0]];\n    };\n    const checkDataTypes = (types) => {\n      const dataTypes = unref(_options.dataTypes);\n      if (typeof dataTypes === \"function\")\n        return dataTypes(types);\n      if (!(dataTypes == null ? void 0 : dataTypes.length))\n        return true;\n      if (types.length === 0)\n        return false;\n      return types.every(\n        (type) => dataTypes.some((allowedType) => type.includes(allowedType))\n      );\n    };\n    const checkValidity = (items) => {\n      const types = Array.from(items != null ? items : []).map((item) => item.type);\n      const dataTypesValid = checkDataTypes(types);\n      const multipleFilesValid = multiple || items.length <= 1;\n      return dataTypesValid && multipleFilesValid;\n    };\n    const isSafari = () => /^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent) && !(\"chrome\" in window);\n    const handleDragEvent = (event, eventType) => {\n      var _a2, _b2, _c, _d, _e, _f;\n      const dataTransferItemList = (_a2 = event.dataTransfer) == null ? void 0 : _a2.items;\n      isValid = (_b2 = dataTransferItemList && checkValidity(dataTransferItemList)) != null ? _b2 : false;\n      if (preventDefaultForUnhandled) {\n        event.preventDefault();\n      }\n      if (!isSafari() && !isValid) {\n        if (event.dataTransfer) {\n          event.dataTransfer.dropEffect = \"none\";\n        }\n        return;\n      }\n      event.preventDefault();\n      if (event.dataTransfer) {\n        event.dataTransfer.dropEffect = \"copy\";\n      }\n      const currentFiles = getFiles(event);\n      switch (eventType) {\n        case \"enter\":\n          counter += 1;\n          isOverDropZone.value = true;\n          (_c = _options.onEnter) == null ? void 0 : _c.call(_options, null, event);\n          break;\n        case \"over\":\n          (_d = _options.onOver) == null ? void 0 : _d.call(_options, null, event);\n          break;\n        case \"leave\":\n          counter -= 1;\n          if (counter === 0)\n            isOverDropZone.value = false;\n          (_e = _options.onLeave) == null ? void 0 : _e.call(_options, null, event);\n          break;\n        case \"drop\":\n          counter = 0;\n          isOverDropZone.value = false;\n          if (isValid) {\n            files.value = currentFiles;\n            (_f = _options.onDrop) == null ? void 0 : _f.call(_options, currentFiles, event);\n          }\n          break;\n      }\n    };\n    useEventListener(target, \"dragenter\", (event) => handleDragEvent(event, \"enter\"));\n    useEventListener(target, \"dragover\", (event) => handleDragEvent(event, \"over\"));\n    useEventListener(target, \"dragleave\", (event) => handleDragEvent(event, \"leave\"));\n    useEventListener(target, \"drop\", (event) => handleDragEvent(event, \"drop\"));\n  }\n  return {\n    files,\n    isOverDropZone\n  };\n}\n\nfunction useResizeObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...observerOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const _targets = toValue(target);\n    return Array.isArray(_targets) ? _targets.map((el) => unrefElement(el)) : [unrefElement(_targets)];\n  });\n  const stopWatch = watch(\n    targets,\n    (els) => {\n      cleanup();\n      if (isSupported.value && window) {\n        observer = new ResizeObserver(callback);\n        for (const _el of els) {\n          if (_el)\n            observer.observe(_el, observerOptions);\n        }\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true,\n    updateTiming = \"sync\"\n  } = options;\n  const height = shallowRef(0);\n  const bottom = shallowRef(0);\n  const left = shallowRef(0);\n  const right = shallowRef(0);\n  const top = shallowRef(0);\n  const width = shallowRef(0);\n  const x = shallowRef(0);\n  const y = shallowRef(0);\n  function recalculate() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  function update() {\n    if (updateTiming === \"sync\")\n      recalculate();\n    else if (updateTiming === \"next-frame\")\n      requestAnimationFrame(() => recalculate());\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  useMutationObserver(target, update, {\n    attributeFilter: [\"style\", \"class\"]\n  });\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useElementByPoint(options) {\n  const {\n    x,\n    y,\n    document = defaultDocument,\n    multiple,\n    interval = \"requestAnimationFrame\",\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => {\n    if (toValue(multiple))\n      return document && \"elementsFromPoint\" in document;\n    return document && \"elementFromPoint\" in document;\n  });\n  const element = shallowRef(null);\n  const cb = () => {\n    var _a, _b;\n    element.value = toValue(multiple) ? (_a = document == null ? void 0 : document.elementsFromPoint(toValue(x), toValue(y))) != null ? _a : [] : (_b = document == null ? void 0 : document.elementFromPoint(toValue(x), toValue(y))) != null ? _b : null;\n  };\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  return {\n    isSupported,\n    element,\n    ...controls\n  };\n}\n\nfunction useElementHover(el, options = {}) {\n  const {\n    delayEnter = 0,\n    delayLeave = 0,\n    triggerOnRemoval = false,\n    window = defaultWindow\n  } = options;\n  const isHovered = shallowRef(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  if (triggerOnRemoval) {\n    onElementRemoval(\n      computed(() => unrefElement(el)),\n      () => toggle(false)\n    );\n  }\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = shallowRef(initialSize.width);\n  const height = shallowRef(initialSize.height);\n  const { stop: stop1 } = useResizeObserver(\n    target,\n    ([entry]) => {\n      const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n      if (window && isSVG.value) {\n        const $elem = unrefElement(target);\n        if ($elem) {\n          const rect = $elem.getBoundingClientRect();\n          width.value = rect.width;\n          height.value = rect.height;\n        }\n      } else {\n        if (boxSize) {\n          const formatBoxSize = toArray(boxSize);\n          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n        } else {\n          width.value = entry.contentRect.width;\n          height.value = entry.contentRect.height;\n        }\n      }\n    },\n    options\n  );\n  tryOnMounted(() => {\n    const ele = unrefElement(target);\n    if (ele) {\n      width.value = \"offsetWidth\" in ele ? ele.offsetWidth : initialSize.width;\n      height.value = \"offsetHeight\" in ele ? ele.offsetHeight : initialSize.height;\n    }\n  });\n  const stop2 = watch(\n    () => unrefElement(target),\n    (ele) => {\n      width.value = ele ? initialSize.width : 0;\n      height.value = ele ? initialSize.height : 0;\n    }\n  );\n  function stop() {\n    stop1();\n    stop2();\n  }\n  return {\n    width,\n    height,\n    stop\n  };\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0,\n    window = defaultWindow,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  const targets = computed(() => {\n    const _target = toValue(target);\n    return toArray(_target).map(unrefElement).filter(notNullish);\n  });\n  let cleanup = noop;\n  const isActive = shallowRef(immediate);\n  const stopWatch = isSupported.value ? watch(\n    () => [targets.value, unrefElement(root), isActive.value],\n    ([targets2, root2]) => {\n      cleanup();\n      if (!isActive.value)\n        return;\n      if (!targets2.length)\n        return;\n      const observer = new IntersectionObserver(\n        callback,\n        {\n          root: unrefElement(root2),\n          rootMargin,\n          threshold\n        }\n      );\n      targets2.forEach((el) => el && observer.observe(el));\n      cleanup = () => {\n        observer.disconnect();\n        cleanup = noop;\n      };\n    },\n    { immediate, flush: \"post\" }\n  ) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n    isActive.value = false;\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    isActive,\n    pause() {\n      cleanup();\n      isActive.value = false;\n    },\n    resume() {\n      isActive.value = true;\n    },\n    stop\n  };\n}\n\nfunction useElementVisibility(element, options = {}) {\n  const {\n    window = defaultWindow,\n    scrollTarget,\n    threshold = 0,\n    rootMargin,\n    once = false\n  } = options;\n  const elementIsVisible = shallowRef(false);\n  const { stop } = useIntersectionObserver(\n    element,\n    (intersectionObserverEntries) => {\n      let isIntersecting = elementIsVisible.value;\n      let latestTime = 0;\n      for (const entry of intersectionObserverEntries) {\n        if (entry.time >= latestTime) {\n          latestTime = entry.time;\n          isIntersecting = entry.isIntersecting;\n        }\n      }\n      elementIsVisible.value = isIntersecting;\n      if (once) {\n        watchOnce(elementIsVisible, () => {\n          stop();\n        });\n      }\n    },\n    {\n      root: scrollTarget,\n      window,\n      threshold,\n      rootMargin: toValue(rootMargin)\n    }\n  );\n  return elementIsVisible;\n}\n\nconst events = /* @__PURE__ */ new Map();\n\n// @__NO_SIDE_EFFECTS__\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || /* @__PURE__ */ new Set();\n    listeners.add(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    listeners.delete(listener);\n    if (!listeners.size)\n      reset();\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction resolveNestedOptions$1(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useEventSource(url, events = [], options = {}) {\n  const event = shallowRef(null);\n  const data = shallowRef(null);\n  const status = shallowRef(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = shallowRef(null);\n  const urlRef = toRef(url);\n  const lastEventId = shallowRef(null);\n  let explicitlyClosed = false;\n  let retried = 0;\n  const {\n    withCredentials = false,\n    immediate = true,\n    autoConnect = true,\n    autoReconnect\n  } = options;\n  const close = () => {\n    if (isClient && eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n      explicitlyClosed = true;\n    }\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const es = new EventSource(urlRef.value, { withCredentials });\n    status.value = \"CONNECTING\";\n    eventSource.value = es;\n    es.onopen = () => {\n      status.value = \"OPEN\";\n      error.value = null;\n    };\n    es.onerror = (e) => {\n      status.value = \"CLOSED\";\n      error.value = e;\n      if (es.readyState === 2 && !explicitlyClosed && autoReconnect) {\n        es.close();\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions$1(autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    es.onmessage = (e) => {\n      event.value = null;\n      data.value = e.data;\n      lastEventId.value = e.lastEventId;\n    };\n    for (const event_name of events) {\n      useEventListener(es, event_name, (e) => {\n        event.value = event_name;\n        data.value = e.data || null;\n        lastEventId.value = e.lastEventId || null;\n      }, { passive: true });\n    }\n  };\n  const open = () => {\n    if (!isClient)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  tryOnScopeDispose(close);\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    open,\n    close,\n    lastEventId\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = shallowRef(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = toRef(newIcon);\n  const applyIcon = (icon) => {\n    const elements = document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`);\n    if (!elements || elements.length === 0) {\n      const link = document == null ? void 0 : document.createElement(\"link\");\n      if (link) {\n        link.rel = rel;\n        link.href = `${baseUrl}${icon}`;\n        link.type = `image/${icon.split(\".\").pop()}`;\n        document == null ? void 0 : document.head.append(link);\n      }\n      return;\n    }\n    elements == null ? void 0 : elements.forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(\n    favicon,\n    (i, o) => {\n      if (typeof i === \"string\" && i !== o)\n        applyIcon(i);\n    },\n    { immediate: true }\n  );\n  return favicon;\n}\n\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\", \"updateDataOnError\");\n}\nconst reAbsolute = /^(?:[a-z][a-z\\d+\\-.]*:)?\\/\\//i;\nfunction isAbsoluteURL(url) {\n  return reAbsolute.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries(headers.entries());\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      let callback;\n      for (let i = callbacks.length - 1; i >= 0; i--) {\n        if (callbacks[i] != null) {\n          callback = callbacks[i];\n          break;\n        }\n      }\n      if (callback)\n        return { ...ctx, ...await callback(ctx) };\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      for (const callback of callbacks) {\n        if (callback)\n          ctx = { ...ctx, ...await callback(ctx) };\n      }\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = toValue(config.baseUrl);\n      const targetUrl = toValue(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = {\n          ...options,\n          ...args[0],\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        };\n      } else {\n        fetchOptions = {\n          ...fetchOptions,\n          ...args[0],\n          headers: {\n            ...headersToObject(fetchOptions.headers) || {},\n            ...headersToObject(args[0].headers) || {}\n          }\n        };\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = {\n        ...options,\n        ...args[1],\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      };\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a, _b;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = {\n    immediate: true,\n    refetch: false,\n    timeout: 0,\n    updateDataOnError: false\n  };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = { ...options, ...args[0] };\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = { ...options, ...args[1] };\n  }\n  const {\n    fetch = (_b = (_a = defaultWindow) == null ? void 0 : _a.fetch) != null ? _b : globalThis == null ? void 0 : globalThis.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = shallowRef(false);\n  const isFetching = shallowRef(false);\n  const aborted = shallowRef(false);\n  const statusCode = shallowRef(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData || null);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = (reason) => {\n    if (supportsAbort) {\n      controller == null ? void 0 : controller.abort(reason);\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = {\n        ...fetchOptions,\n        signal: controller.signal\n      };\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  let executeCounter = 0;\n  const execute = async (throwOnFailed = false) => {\n    var _a2, _b2;\n    abort();\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    executeCounter += 1;\n    const currentExecuteCounter = executeCounter;\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    const payload = toValue(config.payload);\n    if (payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      const proto = Object.getPrototypeOf(payload);\n      if (!config.payloadType && payload && (proto === Object.prototype || Array.isArray(proto)) && !(payload instanceof FormData))\n        config.payloadType = \"json\";\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: toValue(url),\n      options: {\n        ...defaultFetchOptions,\n        ...fetchOptions\n      },\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return fetch(\n      context.url,\n      {\n        ...defaultFetchOptions,\n        ...context.options,\n        headers: {\n          ...headersToObject(defaultFetchOptions.headers),\n          ...headersToObject((_b2 = context.options) == null ? void 0 : _b2.headers)\n        }\n      }\n    ).then(async (fetchResponse) => {\n      response.value = fetchResponse;\n      statusCode.value = fetchResponse.status;\n      responseData = await fetchResponse.clone()[config.type]();\n      if (!fetchResponse.ok) {\n        data.value = initialData || null;\n        throw new Error(fetchResponse.statusText);\n      }\n      if (options.afterFetch) {\n        ({ data: responseData } = await options.afterFetch({\n          data: responseData,\n          response: fetchResponse,\n          context,\n          execute\n        }));\n      }\n      data.value = responseData;\n      responseEvent.trigger(fetchResponse);\n      return fetchResponse;\n    }).catch(async (fetchError) => {\n      let errorData = fetchError.message || fetchError.name;\n      if (options.onFetchError) {\n        ({ error: errorData, data: responseData } = await options.onFetchError({\n          data: responseData,\n          error: fetchError,\n          response: response.value,\n          context,\n          execute\n        }));\n      }\n      error.value = errorData;\n      if (options.updateDataOnError)\n        data.value = responseData;\n      errorEvent.trigger(fetchError);\n      if (throwOnFailed)\n        throw fetchError;\n      return null;\n    }).finally(() => {\n      if (currentExecuteCounter === executeCounter)\n        loading(false);\n      if (timer)\n        timer.stop();\n      finallyEvent.trigger(null);\n    });\n  };\n  const refetch = toRef(options.refetch);\n  watch(\n    [\n      refetch,\n      toRef(url)\n    ],\n    ([refetch2]) => refetch2 && execute(),\n    { deep: true }\n  );\n  const shell = {\n    isFinished: readonly(isFinished),\n    isFetching: readonly(isFetching),\n    statusCode,\n    response,\n    error,\n    data,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    // method\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    // type\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch(\n            [\n              refetch,\n              toRef(config.payload)\n            ],\n            ([refetch2]) => refetch2 && execute(),\n            { deep: true }\n          );\n        }\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    Promise.resolve().then(() => execute());\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  };\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\")) {\n    return `${start}/${end}`;\n  }\n  if (start.endsWith(\"/\") && end.startsWith(\"/\")) {\n    return `${start.slice(0, -1)}${end}`;\n  }\n  return `${start}${end}`;\n}\n\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\",\n  reset: false,\n  directory: false\n};\nfunction prepareInitialFiles(files) {\n  if (!files)\n    return null;\n  if (files instanceof FileList)\n    return files;\n  const dt = new DataTransfer();\n  for (const file of files) {\n    dt.items.add(file);\n  }\n  return dt.files;\n}\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(prepareInitialFiles(options.initialFiles));\n  const { on: onChange, trigger: changeTrigger } = createEventHook();\n  const { on: onCancel, trigger: cancelTrigger } = createEventHook();\n  const inputRef = computed(() => {\n    var _a;\n    const input = (_a = unrefElement(options.input)) != null ? _a : document ? document.createElement(\"input\") : void 0;\n    if (input) {\n      input.type = \"file\";\n      input.onchange = (event) => {\n        const result = event.target;\n        files.value = result.files;\n        changeTrigger(files.value);\n      };\n      input.oncancel = () => {\n        cancelTrigger();\n      };\n    }\n    return input;\n  });\n  const reset = () => {\n    files.value = null;\n    if (inputRef.value && inputRef.value.value) {\n      inputRef.value.value = \"\";\n      changeTrigger(null);\n    }\n  };\n  const applyOptions = (options2) => {\n    const el = inputRef.value;\n    if (!el)\n      return;\n    el.multiple = toValue(options2.multiple);\n    el.accept = toValue(options2.accept);\n    el.webkitdirectory = toValue(options2.directory);\n    if (hasOwn(options2, \"capture\"))\n      el.capture = toValue(options2.capture);\n  };\n  const open = (localOptions) => {\n    const el = inputRef.value;\n    if (!el)\n      return;\n    const mergedOptions = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n      ...localOptions\n    };\n    applyOptions(mergedOptions);\n    if (toValue(mergedOptions.reset))\n      reset();\n    el.click();\n  };\n  watchEffect(() => {\n    applyOptions(options);\n  });\n  return {\n    files: readonly(files),\n    open,\n    reset,\n    onCancel,\n    onChange\n  };\n}\n\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = options;\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = shallowRef();\n  const data = shallowRef();\n  const file = shallowRef();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker({ ...toValue(options), ..._options });\n    fileHandle.value = handle;\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    data.value = void 0;\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    await updateFile();\n    const type = toValue(dataType);\n    if (type === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    else if (type === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    else if (type === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => toValue(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false, focusVisible = false, preventScroll = false } = options;\n  const innerFocused = shallowRef(false);\n  const targetElement = computed(() => unrefElement(target));\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, \"focus\", (event) => {\n    var _a, _b;\n    if (!focusVisible || ((_b = (_a = event.target).matches) == null ? void 0 : _b.call(_a, \":focus-visible\")))\n      innerFocused.value = true;\n  }, listenerOptions);\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false, listenerOptions);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus({ preventScroll });\n    }\n  });\n  watch(\n    targetElement,\n    () => {\n      focused.value = initialValue;\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  return { focused };\n}\n\nconst EVENT_FOCUS_IN = \"focusin\";\nconst EVENT_FOCUS_OUT = \"focusout\";\nconst PSEUDO_CLASS_FOCUS_WITHIN = \":focus-within\";\nfunction useFocusWithin(target, options = {}) {\n  const { window = defaultWindow } = options;\n  const targetElement = computed(() => unrefElement(target));\n  const _focused = shallowRef(false);\n  const focused = computed(() => _focused.value);\n  const activeElement = useActiveElement(options);\n  if (!window || !activeElement.value) {\n    return { focused };\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, EVENT_FOCUS_IN, () => _focused.value = true, listenerOptions);\n  useEventListener(targetElement, EVENT_FOCUS_OUT, () => {\n    var _a, _b, _c;\n    return _focused.value = (_c = (_b = (_a = targetElement.value) == null ? void 0 : _a.matches) == null ? void 0 : _b.call(_a, PSEUDO_CLASS_FOCUS_WITHIN)) != null ? _c : false;\n  }, listenerOptions);\n  return { focused };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useFps(options) {\n  var _a;\n  const fps = shallowRef(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst eventHandlers = [\n  \"fullscreenchange\",\n  \"webkitfullscreenchange\",\n  \"webkitendfullscreen\",\n  \"mozfullscreenchange\",\n  \"MSFullscreenChange\"\n];\nfunction useFullscreen(target, options = {}) {\n  const {\n    document = defaultDocument,\n    autoExit = false\n  } = options;\n  const targetRef = computed(() => {\n    var _a;\n    return (_a = unrefElement(target)) != null ? _a : document == null ? void 0 : document.documentElement;\n  });\n  const isFullscreen = shallowRef(false);\n  const requestMethod = computed(() => {\n    return [\n      \"requestFullscreen\",\n      \"webkitRequestFullscreen\",\n      \"webkitEnterFullscreen\",\n      \"webkitEnterFullScreen\",\n      \"webkitRequestFullScreen\",\n      \"mozRequestFullScreen\",\n      \"msRequestFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const exitMethod = computed(() => {\n    return [\n      \"exitFullscreen\",\n      \"webkitExitFullscreen\",\n      \"webkitExitFullScreen\",\n      \"webkitCancelFullScreen\",\n      \"mozCancelFullScreen\",\n      \"msExitFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenEnabled = computed(() => {\n    return [\n      \"fullScreen\",\n      \"webkitIsFullScreen\",\n      \"webkitDisplayingFullscreen\",\n      \"mozFullScreen\",\n      \"msFullscreenElement\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenElementMethod = [\n    \"fullscreenElement\",\n    \"webkitFullscreenElement\",\n    \"mozFullScreenElement\",\n    \"msFullscreenElement\"\n  ].find((m) => document && m in document);\n  const isSupported = useSupported(() => targetRef.value && document && requestMethod.value !== void 0 && exitMethod.value !== void 0 && fullscreenEnabled.value !== void 0);\n  const isCurrentElementFullScreen = () => {\n    if (fullscreenElementMethod)\n      return (document == null ? void 0 : document[fullscreenElementMethod]) === targetRef.value;\n    return false;\n  };\n  const isElementFullScreen = () => {\n    if (fullscreenEnabled.value) {\n      if (document && document[fullscreenEnabled.value] != null) {\n        return document[fullscreenEnabled.value];\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[fullscreenEnabled.value]) != null) {\n          return Boolean(target2[fullscreenEnabled.value]);\n        }\n      }\n    }\n    return false;\n  };\n  async function exit() {\n    if (!isSupported.value || !isFullscreen.value)\n      return;\n    if (exitMethod.value) {\n      if ((document == null ? void 0 : document[exitMethod.value]) != null) {\n        await document[exitMethod.value]();\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[exitMethod.value]) != null)\n          await target2[exitMethod.value]();\n      }\n    }\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value || isFullscreen.value)\n      return;\n    if (isElementFullScreen())\n      await exit();\n    const target2 = targetRef.value;\n    if (requestMethod.value && (target2 == null ? void 0 : target2[requestMethod.value]) != null) {\n      await target2[requestMethod.value]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    await (isFullscreen.value ? exit() : enter());\n  }\n  const handlerCallback = () => {\n    const isElementFullScreenValue = isElementFullScreen();\n    if (!isElementFullScreenValue || isElementFullScreenValue && isCurrentElementFullScreen())\n      isFullscreen.value = isElementFullScreenValue;\n  };\n  const listenerOptions = { capture: false, passive: true };\n  useEventListener(document, eventHandlers, handlerCallback, listenerOptions);\n  useEventListener(() => unrefElement(targetRef), eventHandlers, handlerCallback, listenerOptions);\n  tryOnMounted(handlerCallback, false);\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\n// @__NO_SIDE_EFFECTS__\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      index: gamepad.index,\n      connected: gamepad.connected,\n      mapping: gamepad.mapping,\n      timestamp: gamepad.timestamp,\n      vibrationActuator: gamepad.vibrationActuator,\n      hapticActuators,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        gamepads.value[gamepad.index] = stateFromGamepad(gamepad);\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  const listenerOptions = { passive: true };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad), listenerOptions);\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad), listenerOptions);\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        onGamepadConnected(gamepad);\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = shallowRef(null);\n  const error = shallowRef(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Number.POSITIVE_INFINITY,\n    longitude: Number.POSITIVE_INFINITY,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(\n        updatePosition,\n        (err) => error.value = err,\n        {\n          enableHighAccuracy,\n          maximumAge,\n          timeout\n        }\n      );\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = shallowRef(initialState);\n  const lastActive = shallowRef(timestamp());\n  let timer;\n  const reset = () => {\n    idle.value = false;\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  };\n  const onEvent = createFilterWrapper(\n    eventFilter,\n    () => {\n      lastActive.value = timestamp();\n      reset();\n    }\n  );\n  if (window) {\n    const document = window.document;\n    const listenerOptions = { passive: true };\n    for (const event of events)\n      useEventListener(window, event, onEvent, listenerOptions);\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      }, listenerOptions);\n    }\n    if (!initialState)\n      reset();\n  }\n  return {\n    idle,\n    lastActive,\n    reset\n  };\n}\n\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes, class: clazz, loading, crossorigin, referrerPolicy, width, height, decoding, fetchPriority, ismap, usemap } = options;\n    img.src = src;\n    if (srcset != null)\n      img.srcset = srcset;\n    if (sizes != null)\n      img.sizes = sizes;\n    if (clazz != null)\n      img.className = clazz;\n    if (loading != null)\n      img.loading = loading;\n    if (crossorigin != null)\n      img.crossOrigin = crossorigin;\n    if (referrerPolicy != null)\n      img.referrerPolicy = referrerPolicy;\n    if (width != null)\n      img.width = width;\n    if (height != null)\n      img.height = height;\n    if (decoding != null)\n      img.decoding = decoding;\n    if (fetchPriority != null)\n      img.fetchPriority = fetchPriority;\n    if (ismap != null)\n      img.isMap = ismap;\n    if (usemap != null)\n      img.useMap = usemap;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nfunction useImage(options, asyncStateOptions = {}) {\n  const state = useAsyncState(\n    () => loadImage(toValue(options)),\n    void 0,\n    {\n      resetOnExecute: true,\n      ...asyncStateOptions\n    }\n  );\n  watch(\n    () => toValue(options),\n    () => state.execute(asyncStateOptions.delay),\n    { deep: true }\n  );\n  return state;\n}\n\nfunction resolveElement(el) {\n  if (typeof Window !== \"undefined\" && el instanceof Window)\n    return el.document.documentElement;\n  if (typeof Document !== \"undefined\" && el instanceof Document)\n    return el.documentElement;\n  return el;\n}\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    observe: _observe = {\n      mutation: false\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\",\n    window = defaultWindow,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const observe = typeof _observe === \"boolean\" ? {\n    mutation: _observe\n  } : _observe;\n  const internalX = shallowRef(0);\n  const internalY = shallowRef(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c, _d;\n    if (!window)\n      return;\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? window.document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = toValue(_y)) != null ? _a : y.value,\n      left: (_b = toValue(_x)) != null ? _b : x.value,\n      behavior: toValue(behavior)\n    });\n    const scrollContainer = ((_d = _element == null ? void 0 : _element.document) == null ? void 0 : _d.documentElement) || (_element == null ? void 0 : _element.documentElement) || _element;\n    if (x != null)\n      internalX.value = scrollContainer.scrollLeft;\n    if (y != null)\n      internalY.value = scrollContainer.scrollTop;\n  }\n  const isScrolling = shallowRef(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const setArrivedState = (target) => {\n    var _a;\n    if (!window)\n      return;\n    const el = ((_a = target == null ? void 0 : target.document) == null ? void 0 : _a.documentElement) || (target == null ? void 0 : target.documentElement) || unrefElement(target);\n    const { display, flexDirection, direction } = getComputedStyle(el);\n    const directionMultipler = direction === \"rtl\" ? -1 : 1;\n    const scrollLeft = el.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalX.value;\n    const left = Math.abs(scrollLeft * directionMultipler) <= (offset.left || 0);\n    const right = Math.abs(scrollLeft * directionMultipler) + el.clientWidth >= el.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"row-reverse\") {\n      arrivedState.left = right;\n      arrivedState.right = left;\n    } else {\n      arrivedState.left = left;\n      arrivedState.right = right;\n    }\n    internalX.value = scrollLeft;\n    let scrollTop = el.scrollTop;\n    if (target === window.document && !scrollTop)\n      scrollTop = window.document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    const top = Math.abs(scrollTop) <= (offset.top || 0);\n    const bottom = Math.abs(scrollTop) + el.clientHeight >= el.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"column-reverse\") {\n      arrivedState.top = bottom;\n      arrivedState.bottom = top;\n    } else {\n      arrivedState.top = top;\n      arrivedState.bottom = bottom;\n    }\n    internalY.value = scrollTop;\n  };\n  const onScrollHandler = (e) => {\n    var _a;\n    if (!window)\n      return;\n    const eventTarget = (_a = e.target.documentElement) != null ? _a : e.target;\n    setArrivedState(eventTarget);\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(\n    element,\n    \"scroll\",\n    throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler,\n    eventListenerOptions\n  );\n  tryOnMounted(() => {\n    try {\n      const _element = toValue(element);\n      if (!_element)\n        return;\n      setArrivedState(_element);\n    } catch (e) {\n      onError(e);\n    }\n  });\n  if ((observe == null ? void 0 : observe.mutation) && element != null && element !== window && element !== document) {\n    useMutationObserver(\n      element,\n      () => {\n        const _element = toValue(element);\n        if (!_element)\n          return;\n        setArrivedState(_element);\n      },\n      {\n        attributes: true,\n        childList: true,\n        subtree: true\n      }\n    );\n  }\n  useEventListener(\n    element,\n    \"scrollend\",\n    onScrollEnd,\n    eventListenerOptions\n  );\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions,\n    measure() {\n      const _element = toValue(element);\n      if (window && _element)\n        setArrivedState(_element);\n    }\n  };\n}\n\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a;\n  const {\n    direction = \"bottom\",\n    interval = 100,\n    canLoadMore = () => true\n  } = options;\n  const state = reactive(useScroll(\n    element,\n    {\n      ...options,\n      offset: {\n        [direction]: (_a = options.distance) != null ? _a : 0,\n        ...options.offset\n      }\n    }\n  ));\n  const promise = ref();\n  const isLoading = computed(() => !!promise.value);\n  const observedElement = computed(() => {\n    return resolveElement(toValue(element));\n  });\n  const isElementVisible = useElementVisibility(observedElement);\n  function checkAndLoad() {\n    state.measure();\n    if (!observedElement.value || !isElementVisible.value || !canLoadMore(observedElement.value))\n      return;\n    const { scrollHeight, clientHeight, scrollWidth, clientWidth } = observedElement.value;\n    const isNarrower = direction === \"bottom\" || direction === \"top\" ? scrollHeight <= clientHeight : scrollWidth <= clientWidth;\n    if (state.arrivedState[direction] || isNarrower) {\n      if (!promise.value) {\n        promise.value = Promise.all([\n          onLoadMore(state),\n          new Promise((resolve) => setTimeout(resolve, interval))\n        ]).finally(() => {\n          promise.value = null;\n          nextTick(() => checkAndLoad());\n        });\n      }\n    }\n  }\n  const stop = watch(\n    () => [state.arrivedState[direction], isElementVisible.value],\n    checkAndLoad,\n    { immediate: true }\n  );\n  tryOnUnmounted(stop);\n  return {\n    isLoading,\n    reset() {\n      nextTick(() => checkAndLoad());\n    }\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\n// @__NO_SIDE_EFFECTS__\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = shallowRef(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      }, { passive: true });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(/* @__PURE__ */ new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = /* @__PURE__ */ new Set();\n  const shiftDeps = /* @__PURE__ */ new Set();\n  const usedKeys = /* @__PURE__ */ new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"shift\" && !value) {\n      const shiftDepsArray = Array.from(shiftDeps);\n      const shiftIndex = shiftDepsArray.indexOf(\"shift\");\n      shiftDepsArray.forEach((key2, index) => {\n        if (index >= shiftIndex) {\n          current.delete(key2);\n          setRefs(key2, false);\n        }\n      });\n      shiftDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Shift\") && value) {\n      [...current, ...values].forEach((key2) => shiftDeps.add(key2));\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive });\n  useEventListener(\"focus\", reset, { passive });\n  const proxy = new Proxy(\n    refs,\n    {\n      get(target2, prop, rec) {\n        if (typeof prop !== \"string\")\n          return Reflect.get(target2, prop, rec);\n        prop = prop.toLowerCase();\n        if (prop in aliasMap)\n          prop = aliasMap[prop];\n        if (!(prop in refs)) {\n          if (/[+_-]/.test(prop)) {\n            const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n            refs[prop] = computed(() => keys.map((key) => toValue(proxy[key])).every(Boolean));\n          } else {\n            refs[prop] = shallowRef(false);\n          }\n        }\n        const r = Reflect.get(target2, prop, rec);\n        return useReactive ? toValue(r) : r;\n      }\n    }\n  );\n  return proxy;\n}\n\nfunction usingElRef(source, cb) {\n  if (toValue(source))\n    cb(toValue(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  target = toRef(target);\n  options = {\n    ...defaultOptions,\n    ...options\n  };\n  const {\n    document = defaultDocument\n  } = options;\n  const listenerOptions = { passive: true };\n  const currentTime = shallowRef(0);\n  const duration = shallowRef(0);\n  const seeking = shallowRef(false);\n  const volume = shallowRef(1);\n  const waiting = shallowRef(false);\n  const ended = shallowRef(false);\n  const playing = shallowRef(false);\n  const rate = shallowRef(1);\n  const stalled = shallowRef(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = shallowRef(-1);\n  const isPictureInPicture = shallowRef(false);\n  const muted = shallowRef(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const playbackErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = typeof track === \"number\" ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = typeof track === \"number\" ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = toValue(target);\n    if (!el)\n      return;\n    const src = toValue(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (typeof src === \"string\")\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type, media }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.setAttribute(\"media\", media || \"\");\n      useEventListener(source, \"error\", sourceErrorEvent.trigger, listenerOptions);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  watch([target, volume], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.volume = volume.value;\n  });\n  watch([target, muted], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.muted = muted.value;\n  });\n  watch([target, rate], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.playbackRate = rate.value;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = toValue(options.tracks);\n    const el = toValue(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    if (isPlaying) {\n      el.play().catch((e) => {\n        playbackErrorEvent.trigger(e);\n        throw e;\n      });\n    } else {\n      el.pause();\n    }\n  });\n  useEventListener(\n    target,\n    \"timeupdate\",\n    () => ignoreCurrentTimeUpdates(() => currentTime.value = toValue(target).currentTime),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"durationchange\",\n    () => duration.value = toValue(target).duration,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"progress\",\n    () => buffered.value = timeRangeToArray(toValue(target).buffered),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeking\",\n    () => seeking.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeked\",\n    () => seeking.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    [\"waiting\", \"loadstart\"],\n    () => {\n      waiting.value = true;\n      ignorePlayingUpdates(() => playing.value = false);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"loadeddata\",\n    () => waiting.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"playing\",\n    () => {\n      waiting.value = false;\n      ended.value = false;\n      ignorePlayingUpdates(() => playing.value = true);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ratechange\",\n    () => rate.value = toValue(target).playbackRate,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"stalled\",\n    () => stalled.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ended\",\n    () => ended.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"pause\",\n    () => ignorePlayingUpdates(() => playing.value = false),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"play\",\n    () => ignorePlayingUpdates(() => playing.value = true),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"enterpictureinpicture\",\n    () => isPictureInPicture.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"leavepictureinpicture\",\n    () => isPictureInPicture.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"volumechange\",\n    () => {\n      const el = toValue(target);\n      if (!el)\n        return;\n      volume.value = el.volume;\n      muted.value = el.muted;\n    },\n    listenerOptions\n  );\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    // Volume\n    volume,\n    muted,\n    // Tracks\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    // Picture in Picture\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    // Events\n    onSourceError: sourceErrorEvent.on,\n    onPlaybackError: playbackErrorEvent.on\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return shallowReactive(options.cache);\n    return shallowReactive(/* @__PURE__ */ new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nconst UseMouseBuiltinExtractors = {\n  page: (event) => [event.pageX, event.pageY],\n  client: (event) => [event.clientX, event.clientY],\n  screen: (event) => [event.screenX, event.screenY],\n  movement: (event) => event instanceof MouseEvent ? [event.movementX, event.movementY] : null\n};\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    target = window,\n    scroll = true,\n    eventFilter\n  } = options;\n  let _prevMouseEvent = null;\n  let _prevScrollX = 0;\n  let _prevScrollY = 0;\n  const x = shallowRef(initialValue.x);\n  const y = shallowRef(initialValue.y);\n  const sourceType = shallowRef(null);\n  const extractor = typeof type === \"function\" ? type : UseMouseBuiltinExtractors[type];\n  const mouseHandler = (event) => {\n    const result = extractor(event);\n    _prevMouseEvent = event;\n    if (result) {\n      [x.value, y.value] = result;\n      sourceType.value = \"mouse\";\n    }\n    if (window) {\n      _prevScrollX = window.scrollX;\n      _prevScrollY = window.scrollY;\n    }\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const result = extractor(event.touches[0]);\n      if (result) {\n        [x.value, y.value] = result;\n        sourceType.value = \"touch\";\n      }\n    }\n  };\n  const scrollHandler = () => {\n    if (!_prevMouseEvent || !window)\n      return;\n    const pos = extractor(_prevMouseEvent);\n    if (_prevMouseEvent instanceof MouseEvent && pos) {\n      x.value = pos[0] + window.scrollX - _prevScrollX;\n      y.value = pos[1] + window.scrollY - _prevScrollY;\n    }\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const mouseHandlerWrapper = eventFilter ? (event) => eventFilter(() => mouseHandler(event), {}) : (event) => mouseHandler(event);\n  const touchHandlerWrapper = eventFilter ? (event) => eventFilter(() => touchHandler(event), {}) : (event) => touchHandler(event);\n  const scrollHandlerWrapper = eventFilter ? () => eventFilter(() => scrollHandler(), {}) : () => scrollHandler();\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"mousemove\", \"dragover\"], mouseHandlerWrapper, listenerOptions);\n    if (touch && type !== \"movement\") {\n      useEventListener(target, [\"touchstart\", \"touchmove\"], touchHandlerWrapper, listenerOptions);\n      if (resetOnTouchEnds)\n        useEventListener(target, \"touchend\", reset, listenerOptions);\n    }\n    if (scroll && type === \"page\")\n      useEventListener(window, \"scroll\", scrollHandlerWrapper, listenerOptions);\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    windowResize = true,\n    windowScroll = true,\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const type = options.type || \"page\";\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = shallowRef(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = shallowRef(0);\n  const elementY = shallowRef(0);\n  const elementPositionX = shallowRef(0);\n  const elementPositionY = shallowRef(0);\n  const elementHeight = shallowRef(0);\n  const elementWidth = shallowRef(0);\n  const isOutside = shallowRef(true);\n  function update() {\n    if (!window)\n      return;\n    const el = unrefElement(targetRef);\n    if (!el || !(el instanceof Element))\n      return;\n    const {\n      left,\n      top,\n      width,\n      height\n    } = el.getBoundingClientRect();\n    elementPositionX.value = left + (type === \"page\" ? window.pageXOffset : 0);\n    elementPositionY.value = top + (type === \"page\" ? window.pageYOffset : 0);\n    elementHeight.value = height;\n    elementWidth.value = width;\n    const elX = x.value - elementPositionX.value;\n    const elY = y.value - elementPositionY.value;\n    isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n    if (handleOutside || !isOutside.value) {\n      elementX.value = elX;\n      elementY.value = elY;\n    }\n  }\n  const stopFnList = [];\n  function stop() {\n    stopFnList.forEach((fn) => fn());\n    stopFnList.length = 0;\n  }\n  tryOnMounted(() => {\n    update();\n  });\n  if (window) {\n    const {\n      stop: stopResizeObserver\n    } = useResizeObserver(targetRef, update);\n    const {\n      stop: stopMutationObserver\n    } = useMutationObserver(targetRef, update, {\n      attributeFilter: [\"style\", \"class\"]\n    });\n    const stopWatch = watch(\n      [targetRef, x, y],\n      update\n    );\n    stopFnList.push(\n      stopResizeObserver,\n      stopMutationObserver,\n      stopWatch\n    );\n    useEventListener(\n      document,\n      \"mouseleave\",\n      () => isOutside.value = true,\n      { passive: true }\n    );\n    if (windowScroll) {\n      stopFnList.push(\n        useEventListener(\"scroll\", update, { capture: true, passive: true })\n      );\n    }\n    if (windowResize) {\n      stopFnList.push(\n        useEventListener(\"resize\", update, { passive: true })\n      );\n    }\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    capture = false,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = shallowRef(initialValue);\n  const sourceType = shallowRef(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => (event) => {\n    var _a;\n    pressed.value = true;\n    sourceType.value = srcType;\n    (_a = options.onPressed) == null ? void 0 : _a.call(options, event);\n  };\n  const onReleased = (event) => {\n    var _a;\n    pressed.value = false;\n    sourceType.value = null;\n    (_a = options.onReleased) == null ? void 0 : _a.call(options, event);\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  const listenerOptions = { passive: true, capture };\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), listenerOptions);\n  useEventListener(window, \"mouseleave\", onReleased, listenerOptions);\n  useEventListener(window, \"mouseup\", onReleased, listenerOptions);\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), listenerOptions);\n    useEventListener(window, \"drop\", onReleased, listenerOptions);\n    useEventListener(window, \"dragend\", onReleased, listenerOptions);\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), listenerOptions);\n    useEventListener(window, \"touchend\", onReleased, listenerOptions);\n    useEventListener(window, \"touchcancel\", onReleased, listenerOptions);\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useNavigatorLanguage(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = shallowRef(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  }, { passive: true });\n  return {\n    isSupported,\n    language\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = shallowRef(true);\n  const saveData = shallowRef(false);\n  const offlineAt = shallowRef(void 0);\n  const onlineAt = shallowRef(void 0);\n  const downlink = shallowRef(void 0);\n  const downlinkMax = shallowRef(void 0);\n  const rtt = shallowRef(void 0);\n  const effectiveType = shallowRef(void 0);\n  const type = shallowRef(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  const listenerOptions = { passive: true };\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    }, listenerOptions);\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    }, listenerOptions);\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, listenerOptions);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline: readonly(isOnline),\n    saveData: readonly(saveData),\n    offlineAt: readonly(offlineAt),\n    onlineAt: readonly(onlineAt),\n    downlink: readonly(downlink),\n    downlinkMax: readonly(downlinkMax),\n    effectiveType: readonly(effectiveType),\n    rtt: readonly(rtt),\n    type: readonly(type)\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\",\n    immediate = true\n  } = options;\n  const now = ref(/* @__PURE__ */ new Date());\n  const update = () => now.value = /* @__PURE__ */ new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate }) : useIntervalFn(update, interval, { immediate });\n  if (exposeControls) {\n    return {\n      now,\n      ...controls\n    };\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = shallowRef();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(\n    () => toValue(object),\n    (newObject) => {\n      release();\n      if (newObject)\n        url.value = URL.createObjectURL(newObject);\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useClamp(value, min, max) {\n  if (typeof value === \"function\" || isReadonly(value))\n    return computed(() => clamp(toValue(value), toValue(min), toValue(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, toValue(min), toValue(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, toValue(min), toValue(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Number.POSITIVE_INFINITY,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Number.POSITIVE_INFINITY);\n  const pageCount = computed(() => Math.max(\n    1,\n    Math.ceil(toValue(total) / toValue(currentPageSize))\n  ));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page)) {\n    syncRef(page, currentPage, {\n      direction: isReadonly(page) ? \"ltr\" : \"both\"\n    });\n  }\n  if (isRef(pageSize)) {\n    syncRef(pageSize, currentPageSize, {\n      direction: isReadonly(pageSize) ? \"ltr\" : \"both\"\n    });\n  }\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = shallowRef(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"mouseout\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseleave\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseenter\", handler, listenerOptions);\n  }\n  return isLeft;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useScreenOrientation(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = shallowRef(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    }, { passive: true });\n  }\n  const lockOrientation = (type) => {\n    if (isSupported.value && typeof screenOrientation.lock === \"function\")\n      return screenOrientation.lock(type);\n    return Promise.reject(new Error(\"Not supported\"));\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value && typeof screenOrientation.unlock === \"function\")\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const screenOrientation = reactive(useScreenOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0)) {\n      return \"deviceOrientation\";\n    }\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        case \"portrait-primary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-secondary\":\n          value = orientation.beta / 90;\n          break;\n        default:\n          value = -orientation.beta / 90;\n      }\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.beta / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"portrait-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        default:\n          value = orientation.gamma / 90;\n      }\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nfunction useParentElement(element = useCurrentElement()) {\n  const parentElement = shallowRef();\n  const update = () => {\n    const el = unrefElement(element);\n    if (el)\n      parentElement.value = el.parentElement;\n  };\n  tryOnMounted(update);\n  watch(() => toValue(element), update);\n  return parentElement;\n}\n\nfunction usePerformanceObserver(options, callback) {\n  const {\n    window = defaultWindow,\n    immediate = true,\n    ...performanceOptions\n  } = options;\n  const isSupported = useSupported(() => window && \"PerformanceObserver\" in window);\n  let observer;\n  const stop = () => {\n    observer == null ? void 0 : observer.disconnect();\n  };\n  const start = () => {\n    if (isSupported.value) {\n      stop();\n      observer = new PerformanceObserver(callback);\n      observer.observe(performanceOptions);\n    }\n  };\n  tryOnScopeDispose(stop);\n  if (immediate)\n    start();\n  return {\n    isSupported,\n    start,\n    stop\n  };\n}\n\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = shallowRef(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"pointerdown\", \"pointermove\", \"pointerup\"], handler, listenerOptions);\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, listenerOptions);\n  }\n  return {\n    ...toRefs(state),\n    isInside\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = shallowRef();\n  const triggerElement = shallowRef();\n  let targetElement;\n  if (isSupported.value) {\n    const listenerOptions = { passive: true };\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    }, listenerOptions);\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    }, listenerOptions);\n  }\n  async function lock(e) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock();\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = toRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    disableTextSelect = false\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const isPointerDown = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return distanceY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const listenerOptions = { passive: true };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointerup\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n    }, listenerOptions)\n  ];\n  tryOnMounted(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"pan-y\");\n    if (disableTextSelect) {\n      (_d = (_c = targetRef.value) == null ? void 0 : _c.style) == null ? void 0 : _d.setProperty(\"-webkit-user-select\", \"none\");\n      (_f = (_e = targetRef.value) == null ? void 0 : _e.style) == null ? void 0 : _f.setProperty(\"-ms-user-select\", \"none\");\n      (_h = (_g = targetRef.value) == null ? void 0 : _g.style) == null ? void 0 : _h.setProperty(\"user-select\", \"none\");\n    }\n  });\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  }, { passive: true });\n  return value;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction usePreferredReducedTransparency(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-transparency: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(\n    toRef(value),\n    (_, oldValue) => {\n      previous.value = oldValue;\n    },\n    { flush: \"sync\" }\n  );\n  return readonly(previous);\n}\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = shallowRef(\"\");\n  const right = shallowRef(\"\");\n  const bottom = shallowRef(\"\");\n  const left = shallowRef(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    tryOnMounted(update);\n    useEventListener(\"resize\", useDebounceFn(update), { passive: true });\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {},\n    nonce = void 0\n  } = options;\n  const scriptTag = shallowRef(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = toValue(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      if (nonce) {\n        el.nonce = nonce;\n      }\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    const listenerOptions = {\n      passive: true\n    };\n    useEventListener(el, \"error\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"abort\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    }, listenerOptions);\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientWidth < ele.scrollWidth || style.overflowY === \"auto\" && ele.clientHeight < ele.scrollHeight) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nconst elInitialOverflow = /* @__PURE__ */ new WeakMap();\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = shallowRef(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow = \"\";\n  watch(toRef(element), (el) => {\n    const target = resolveElement(toValue(el));\n    if (target) {\n      const ele = target;\n      if (!elInitialOverflow.get(ele))\n        elInitialOverflow.set(ele, ele.style.overflow);\n      if (ele.style.overflow !== \"hidden\")\n        initialOverflow = ele.style.overflow;\n      if (ele.style.overflow === \"hidden\")\n        return isLocked.value = true;\n      if (isLocked.value)\n        return ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        el,\n        \"touchmove\",\n        (e) => {\n          preventDefault(e);\n        },\n        { passive: false }\n      );\n    }\n    el.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || !isLocked.value)\n      return;\n    if (isIOS)\n      stopTouchMoveListener == null ? void 0 : stopTouchMoveListener();\n    el.style.overflow = initialOverflow;\n    elInitialOverflow.delete(el);\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = {\n        ...toValue(shareOptions),\n        ...toValue(overrideOptions)\n      };\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...toValue(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(toValue(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    maxAlternatives = 1,\n    window = defaultWindow\n  } = options;\n  const lang = toRef(options.lang || \"en-US\");\n  const isListening = shallowRef(false);\n  const isFinal = shallowRef(false);\n  const result = shallowRef(\"\");\n  const error = shallowRef(void 0);\n  let recognition;\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const toggle = (value = !isListening.value) => {\n    if (value) {\n      start();\n    } else {\n      stop();\n    }\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = toValue(lang);\n    recognition.maxAlternatives = maxAlternatives;\n    recognition.onstart = () => {\n      isListening.value = true;\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const currentResult = event.results[event.resultIndex];\n      const { transcript } = currentResult[0];\n      isFinal.value = currentResult.isFinal;\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = toValue(lang);\n    };\n    watch(isListening, (newValue, oldValue) => {\n      if (newValue === oldValue)\n        return;\n      if (newValue)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow,\n    onBoundary\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = shallowRef(false);\n  const status = shallowRef(\"init\");\n  const spokenText = toRef(text || \"\");\n  const lang = toRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = toValue(lang);\n    utterance2.voice = toValue(options.voice) || null;\n    utterance2.pitch = toValue(pitch);\n    utterance2.rate = toValue(rate);\n    utterance2.volume = toValue(volume);\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n    utterance2.onboundary = (event) => {\n      onBoundary == null ? void 0 : onBoundary(event);\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    if (utterance)\n      synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    onReady\n  } = options;\n  const rawInit = toValue(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(toValue(initialValue));\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorageAsync\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (typeof mergeDefaults === \"function\")\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = { ...rawInit, ...value };\n        else data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  const promise = new Promise((resolve) => {\n    read().then(() => {\n      onReady == null ? void 0 : onReady(data.value);\n      resolve(data);\n    });\n  });\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => Promise.resolve().then(() => read(e)), { passive: true });\n  if (storage) {\n    watchWithFilter(\n      data,\n      async () => {\n        try {\n          if (data.value == null)\n            await storage.removeItem(key);\n          else\n            await storage.setItem(key, await serializer.write(data.value));\n        } catch (e) {\n          onError(e);\n        }\n      },\n      {\n        flush,\n        deep,\n        eventFilter\n      }\n    );\n  }\n  Object.assign(data, {\n    then: promise.then.bind(promise),\n    catch: promise.catch.bind(promise)\n  });\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = shallowRef(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = shallowRef(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.id = id;\n      if (options.nonce)\n        el.nonce = options.nonce;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(\n      cssRef,\n      (value) => {\n        el.textContent = value;\n      },\n      { immediate: true }\n    );\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return diffY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  const listenerOptions = { passive, capture: !passive };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (listenerOptions.capture && !listenerOptions.passive && Math.abs(diffX.value) > Math.abs(diffY.value))\n        e.preventDefault();\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, [\"touchend\", \"touchcancel\"], onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop,\n    // TODO: Remove in the next major version\n    isPassiveEventSupported: true\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(\n      document.querySelector(selector),\n      () => dir.value = getValue(),\n      { attributes: true }\n    );\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  return Array.from({ length: rangeCount }, (_, i) => selection.getRangeAt(i));\n}\n// @__NO_SIDE_EFFECTS__\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange, { passive: true });\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction tryRequestAnimationFrame(window = defaultWindow, fn) {\n  if (window && typeof window.requestAnimationFrame === \"function\") {\n    window.requestAnimationFrame(fn);\n  } else {\n    fn();\n  }\n}\nfunction useTextareaAutosize(options = {}) {\n  var _a, _b;\n  const { window = defaultWindow } = options;\n  const textarea = toRef(options == null ? void 0 : options.element);\n  const input = toRef((_a = options == null ? void 0 : options.input) != null ? _a : \"\");\n  const styleProp = (_b = options == null ? void 0 : options.styleProp) != null ? _b : \"height\";\n  const textareaScrollHeight = shallowRef(1);\n  const textareaOldWidth = shallowRef(0);\n  function triggerResize() {\n    var _a2;\n    if (!textarea.value)\n      return;\n    let height = \"\";\n    textarea.value.style[styleProp] = \"1px\";\n    textareaScrollHeight.value = (_a2 = textarea.value) == null ? void 0 : _a2.scrollHeight;\n    const _styleTarget = toValue(options == null ? void 0 : options.styleTarget);\n    if (_styleTarget)\n      _styleTarget.style[styleProp] = `${textareaScrollHeight.value}px`;\n    else\n      height = `${textareaScrollHeight.value}px`;\n    textarea.value.style[styleProp] = height;\n  }\n  watch([input, textarea], () => nextTick(triggerResize), { immediate: true });\n  watch(textareaScrollHeight, () => {\n    var _a2;\n    return (_a2 = options == null ? void 0 : options.onResize) == null ? void 0 : _a2.call(options);\n  });\n  useResizeObserver(textarea, ([{ contentRect }]) => {\n    if (textareaOldWidth.value === contentRect.width)\n      return;\n    tryRequestAnimationFrame(window, () => {\n      textareaOldWidth.value = contentRect.width;\n      triggerResize();\n    });\n  });\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nfunction DEFAULT_FORMATTER(date) {\n  return date.toISOString().slice(0, 10);\n}\n// @__NO_SIDE_EFFECTS__\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const { now, ...controls } = useNow({ interval: updateInterval, controls: true });\n  const timeAgo = computed(() => formatTimeAgo(new Date(toValue(time)), options, toValue(now)));\n  if (exposeControls) {\n    return {\n      timeAgo,\n      ...controls\n    };\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDateFormatter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDateFormatter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nconst UNITS = [\n  { name: \"year\", ms: 31536e6 },\n  { name: \"month\", ms: 2592e6 },\n  { name: \"week\", ms: 6048e5 },\n  { name: \"day\", ms: 864e5 },\n  { name: \"hour\", ms: 36e5 },\n  { name: \"minute\", ms: 6e4 },\n  { name: \"second\", ms: 1e3 }\n];\nfunction useTimeAgoIntl(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const { now, ...controls } = useNow({ interval: updateInterval, controls: true });\n  const result = computed(\n    () => getTimeAgoIntlResult(new Date(toValue(time)), options, toValue(now))\n  );\n  const parts = computed(() => result.value.parts);\n  const timeAgoIntl = computed(\n    () => formatTimeAgoIntlParts(parts.value, {\n      ...options,\n      locale: result.value.resolvedLocale\n    })\n  );\n  return exposeControls ? { timeAgoIntl, parts, ...controls } : timeAgoIntl;\n}\nfunction formatTimeAgoIntl(from, options = {}, now = Date.now()) {\n  const { parts, resolvedLocale } = getTimeAgoIntlResult(from, options, now);\n  return formatTimeAgoIntlParts(parts, {\n    ...options,\n    locale: resolvedLocale\n  });\n}\nfunction getTimeAgoIntlResult(from, options = {}, now = Date.now()) {\n  const {\n    locale,\n    relativeTimeFormatOptions = { numeric: \"auto\" }\n  } = options;\n  const rtf = new Intl.RelativeTimeFormat(locale, relativeTimeFormatOptions);\n  const { locale: resolvedLocale } = rtf.resolvedOptions();\n  const diff = +from - +now;\n  const absDiff = Math.abs(diff);\n  for (const { name, ms } of UNITS) {\n    if (absDiff >= ms) {\n      return {\n        resolvedLocale,\n        parts: rtf.formatToParts(Math.round(diff / ms), name)\n      };\n    }\n  }\n  return {\n    resolvedLocale,\n    parts: rtf.formatToParts(0, \"second\")\n  };\n}\nfunction formatTimeAgoIntlParts(parts, options = {}) {\n  const {\n    insertSpace = true,\n    joinParts,\n    locale\n  } = options;\n  if (typeof joinParts === \"function\")\n    return joinParts(parts, locale);\n  if (!insertSpace)\n    return parts.map((part) => part.value).join(\"\");\n  return parts.map((part) => part.value.trim()).join(\" \");\n}\n\nfunction useTimeoutPoll(fn, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const { start } = useTimeoutFn(loop, interval, { immediate });\n  const isActive = shallowRef(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      if (immediateCallback)\n        fn();\n      start();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (immediate && isClient)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = shallowRef(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return {\n      timestamp: ts,\n      ...controls\n    };\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b, _c;\n  const {\n    document = defaultDocument,\n    restoreOnUnmount = (t) => t\n  } = options;\n  const originalTitle = (_a = document == null ? void 0 : document.title) != null ? _a : \"\";\n  const title = toRef((_b = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _b : null);\n  const isReadonly = !!(newTitle && typeof newTitle === \"function\");\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return typeof template === \"function\" ? template(t) : toValue(template).replace(/%s/g, t);\n  }\n  watch(\n    title,\n    (newValue, oldValue) => {\n      if (newValue !== oldValue && document)\n        document.title = format(newValue != null ? newValue : \"\");\n    },\n    { immediate: true }\n  );\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver(\n      (_c = document.head) == null ? void 0 : _c.querySelector(\"title\"),\n      () => {\n        if (document && document.title !== title.value)\n          title.value = format(document.title);\n      },\n      { childList: true }\n    );\n  }\n  tryOnScopeDispose(() => {\n    if (restoreOnUnmount) {\n      const restoredTitle = restoreOnUnmount(originalTitle, title.value || \"\");\n      if (restoredTitle != null && document)\n        document.title = restoredTitle;\n    }\n  });\n  return title;\n}\n\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = /* @__PURE__ */ Object.assign({}, { linear: identity }, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction lerp(a, b, alpha) {\n  return a + alpha * (b - a);\n}\nfunction toVec(t) {\n  return (typeof t === \"number\" ? [t] : t) || [];\n}\nfunction executeTransition(source, from, to, options = {}) {\n  var _a, _b;\n  const {\n    window = defaultWindow\n  } = options;\n  const fromVal = toValue(from);\n  const toVal = toValue(to);\n  const v1 = toVec(fromVal);\n  const v2 = toVec(toVal);\n  const duration = (_a = toValue(options.duration)) != null ? _a : 1e3;\n  const startedAt = Date.now();\n  const endAt = Date.now() + duration;\n  const trans = typeof options.transition === \"function\" ? options.transition : (_b = toValue(options.transition)) != null ? _b : identity;\n  const ease = typeof trans === \"function\" ? trans : createEasingFunction(trans);\n  return new Promise((resolve) => {\n    source.value = fromVal;\n    const tick = () => {\n      var _a2;\n      if ((_a2 = options.abort) == null ? void 0 : _a2.call(options)) {\n        resolve();\n        return;\n      }\n      const now = Date.now();\n      const alpha = ease((now - startedAt) / duration);\n      const arr = toVec(source.value).map((n, i) => lerp(v1[i], v2[i], alpha));\n      if (Array.isArray(source.value))\n        source.value = arr.map((n, i) => {\n          var _a3, _b2;\n          return lerp((_a3 = v1[i]) != null ? _a3 : 0, (_b2 = v2[i]) != null ? _b2 : 0, alpha);\n        });\n      else if (typeof source.value === \"number\")\n        source.value = arr[0];\n      if (now < endAt) {\n        window == null ? void 0 : window.requestAnimationFrame(tick);\n      } else {\n        source.value = toVal;\n        resolve();\n      }\n    };\n    tick();\n  });\n}\nfunction useTransition(source, options = {}) {\n  let currentId = 0;\n  const sourceVal = () => {\n    const v = toValue(source);\n    return typeof v === \"number\" ? v : v.map(toValue);\n  };\n  const outputRef = ref(sourceVal());\n  watch(sourceVal, async (to) => {\n    var _a, _b;\n    if (toValue(options.disabled))\n      return;\n    const id = ++currentId;\n    if (options.delay)\n      await promiseTimeout(toValue(options.delay));\n    if (id !== currentId)\n      return;\n    const toVal = Array.isArray(to) ? to.map(toValue) : toValue(to);\n    (_a = options.onStarted) == null ? void 0 : _a.call(options);\n    await executeTransition(outputRef, outputRef.value, toVal, {\n      ...options,\n      abort: () => {\n        var _a2;\n        return id !== currentId || ((_a2 = options.abort) == null ? void 0 : _a2.call(options));\n      }\n    });\n    (_b = options.onFinished) == null ? void 0 : _b.call(options);\n  }, { deep: true });\n  watch(() => toValue(options.disabled), (disabled) => {\n    if (disabled) {\n      currentId++;\n      outputRef.value = sourceVal();\n    }\n  });\n  tryOnScopeDispose(() => {\n    currentId++;\n  });\n  return computed(() => toValue(options.disabled) ? sourceVal() : outputRef.value);\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    writeMode = \"replace\",\n    window = defaultWindow,\n    stringify = (params) => params.toString()\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = stringify(params);\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${window.location.search || \"\"}${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${window.location.search || \"\"}${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(\n    state,\n    () => {\n      const params = new URLSearchParams(\"\");\n      Object.keys(state).forEach((key) => {\n        const mapEntry = state[key];\n        if (Array.isArray(mapEntry))\n          mapEntry.forEach((value) => params.append(key, value));\n        else if (removeNullishValues && mapEntry == null)\n          params.delete(key);\n        else if (removeFalsyValues && !mapEntry)\n          params.delete(key);\n        else\n          params.set(key, mapEntry);\n      });\n      write(params, false);\n    },\n    { deep: true }\n  );\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    if (writeMode === \"replace\") {\n      window.history.replaceState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    } else {\n      window.history.pushState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    }\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"popstate\", onChanged, listenerOptions);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, listenerOptions);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = shallowRef((_b = options.autoSwitch) != null ? _b : true);\n  const constraints = ref(options.constraints);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(type) {\n    switch (type) {\n      case \"video\": {\n        if (constraints.value)\n          return constraints.value.video || false;\n        break;\n      }\n      case \"audio\": {\n        if (constraints.value)\n          return constraints.value.audio || false;\n        break;\n      }\n    }\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(\"video\"),\n      audio: getDeviceOptions(\"audio\")\n    });\n    return stream.value;\n  }\n  function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else _stop();\n    },\n    { immediate: true }\n  );\n  watch(\n    constraints,\n    () => {\n      if (autoSwitch.value && stream.value)\n        restart();\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    constraints,\n    enabled,\n    autoSwitch\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue,\n    shouldEmit\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    key = \"modelValue\";\n  }\n  event = event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : typeof clone === \"function\" ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  const triggerEmit = (value) => {\n    if (shouldEmit) {\n      if (shouldEmit(value))\n        _emit(event, value);\n    } else {\n      _emit(event, value);\n    }\n  };\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    let isUpdating = false;\n    watch(\n      () => props[key],\n      (v) => {\n        if (!isUpdating) {\n          isUpdating = true;\n          proxy.value = cloneFn(v);\n          nextTick(() => isUpdating = false);\n        }\n      }\n    );\n    watch(\n      proxy,\n      (v) => {\n        if (!isUpdating && (v !== props[key] || deep))\n          triggerEmit(v);\n      },\n      { deep }\n    );\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        triggerEmit(value);\n      }\n    });\n  }\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props) {\n    ret[key] = useVModel(\n      props,\n      key,\n      emit,\n      options\n    );\n  }\n  return ret;\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = toRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(\n      vibrate,\n      interval,\n      {\n        immediate: false,\n        immediateCallback: false\n      }\n    );\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = shallowRef(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, containerRef, calculateRange) {\n  watch([size.width, size.height, () => toValue(list), containerRef], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useWakeLock(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  const requestedType = shallowRef(false);\n  const sentinel = shallowRef(null);\n  const documentVisibility = useDocumentVisibility({ document });\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = computed(() => !!sentinel.value && documentVisibility.value === \"visible\");\n  if (isSupported.value) {\n    useEventListener(sentinel, \"release\", () => {\n      var _a, _b;\n      requestedType.value = (_b = (_a = sentinel.value) == null ? void 0 : _a.type) != null ? _b : false;\n    }, { passive: true });\n    whenever(\n      () => documentVisibility.value === \"visible\" && (document == null ? void 0 : document.visibilityState) === \"visible\" && requestedType.value,\n      (type) => {\n        requestedType.value = false;\n        forceRequest(type);\n      }\n    );\n  }\n  async function forceRequest(type) {\n    var _a;\n    await ((_a = sentinel.value) == null ? void 0 : _a.release());\n    sentinel.value = isSupported.value ? await navigator.wakeLock.request(type) : null;\n  }\n  async function request(type) {\n    if (documentVisibility.value === \"visible\")\n      await forceRequest(type);\n    else\n      requestedType.value = type;\n  }\n  async function release() {\n    requestedType.value = false;\n    const s = sentinel.value;\n    sentinel.value = null;\n    await (s == null ? void 0 : s.release());\n  }\n  return {\n    sentinel,\n    isSupported,\n    isActive,\n    request,\n    forceRequest,\n    release\n  };\n}\n\nfunction useWebNotification(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions: _requestForPermissions = true\n  } = options;\n  const defaultWebNotificationOptions = options;\n  const isSupported = useSupported(() => {\n    if (!window || !(\"Notification\" in window))\n      return false;\n    if (Notification.permission === \"granted\")\n      return true;\n    try {\n      const notification2 = new Notification(\"\");\n      notification2.onshow = () => {\n        notification2.close();\n      };\n    } catch (e) {\n      if (e.name === \"TypeError\")\n        return false;\n    }\n    return true;\n  });\n  const permissionGranted = shallowRef(isSupported.value && \"permission\" in Notification && Notification.permission === \"granted\");\n  const notification = ref(null);\n  const ensurePermissions = async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionGranted.value && Notification.permission !== \"denied\") {\n      const result = await Notification.requestPermission();\n      if (result === \"granted\")\n        permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  };\n  const { on: onClick, trigger: clickTrigger } = createEventHook();\n  const { on: onShow, trigger: showTrigger } = createEventHook();\n  const { on: onError, trigger: errorTrigger } = createEventHook();\n  const { on: onClose, trigger: closeTrigger } = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value || !permissionGranted.value)\n      return;\n    const options2 = Object.assign({}, defaultWebNotificationOptions, overrides);\n    notification.value = new Notification(options2.title || \"\", options2);\n    notification.value.onclick = clickTrigger;\n    notification.value.onshow = showTrigger;\n    notification.value.onerror = errorTrigger;\n    notification.value.onclose = closeTrigger;\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  if (_requestForPermissions)\n    tryOnMounted(ensurePermissions);\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    ensurePermissions,\n    permissionGranted,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n}\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoConnect = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = shallowRef(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = toRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let retryTimeout;\n  let pongTimeoutWait;\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetRetry = () => {\n    if (retryTimeout != null) {\n      clearTimeout(retryTimeout);\n      retryTimeout = void 0;\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const close = (code = 1e3, reason) => {\n    resetRetry();\n    if (!isClient && !isWorker || !wsRef.value)\n      return;\n    explicitlyClosed = true;\n    resetHeartbeat();\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n    wsRef.value = void 0;\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      retried = 0;\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      resetHeartbeat();\n      heartbeatPause == null ? void 0 : heartbeatPause();\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect && (wsRef.value == null || ws === wsRef.value)) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        const checkRetires = typeof retries === \"function\" ? retries : () => typeof retries === \"number\" && (retries < 0 || retried < retries);\n        if (checkRetires(retried)) {\n          retried += 1;\n          retryTimeout = setTimeout(_init, delay);\n        } else {\n          onFailed == null ? void 0 : onFailed();\n        }\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE,\n          responseMessage = message\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === toValue(responseMessage))\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(\n      () => {\n        send(toValue(message), false);\n        if (pongTimeoutWait != null)\n          return;\n        pongTimeoutWait = setTimeout(() => {\n          close();\n          explicitlyClosed = false;\n        }, pongTimeout);\n      },\n      interval,\n      { immediate: false }\n    );\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    if (isClient)\n      useEventListener(\"beforeunload\", () => close(), { passive: true });\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    if (!isClient && !isWorker)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = (...args) => {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(...args);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (typeof arg0 === \"string\")\n      worker.value = new Worker(arg0, workerOptions);\n    else if (typeof arg0 === \"function\")\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nfunction depsParser(deps, localDeps) {\n  if (deps.length === 0 && localDeps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  const depsFunctionString = localDeps.filter((dep) => typeof dep === \"function\").map((fn) => {\n    const str = fn.toString();\n    if (str.trim().startsWith(\"function\")) {\n      return str;\n    } else {\n      const name = fn.name;\n      return `const ${name} = ${str}`;\n    }\n  }).join(\";\");\n  const importString = `importScripts(${depsString});`;\n  return `${depsString.trim() === \"\" ? \"\" : importString} ${depsFunctionString}`;\n}\n\nfunction jobRunner(userFunc) {\n  return (e) => {\n    const userFuncArgs = e.data[0];\n    return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n      postMessage([\"SUCCESS\", result]);\n    }).catch((error) => {\n      postMessage([\"ERROR\", error]);\n    });\n  };\n}\n\nfunction createWorkerBlobUrl(fn, deps, localDeps) {\n  const blobCode = `${depsParser(deps, localDeps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n}\n\nfunction useWebWorkerFn(fn, options = {}) {\n  const {\n    dependencies = [],\n    localDependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = shallowRef(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = shallowRef();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies, localDependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      e.preventDefault();\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(\n        () => workerTerminate(\"TIMEOUT_EXPIRED\"),\n        timeout\n      );\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    var _a;\n    promise.value = {\n      resolve,\n      reject\n    };\n    (_a = worker.value) == null ? void 0 : _a.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\n        \"[useWebWorkerFn] You can only run one instance of the worker at a time.\"\n      );\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useWindowFocus(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return shallowRef(false);\n  const focused = shallowRef(window.document.hasFocus());\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  }, listenerOptions);\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  }, listenerOptions);\n  return focused;\n}\n\nfunction useWindowScroll(options = {}) {\n  const { window = defaultWindow, ...rest } = options;\n  return useScroll(window, rest);\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Number.POSITIVE_INFINITY,\n    initialHeight = Number.POSITIVE_INFINITY,\n    listenOrientation = true,\n    includeScrollbar = true,\n    type = \"inner\"\n  } = options;\n  const width = shallowRef(initialWidth);\n  const height = shallowRef(initialHeight);\n  const update = () => {\n    if (window) {\n      if (type === \"outer\") {\n        width.value = window.outerWidth;\n        height.value = window.outerHeight;\n      } else if (type === \"visual\" && window.visualViewport) {\n        const { width: visualViewportWidth, height: visualViewportHeight, scale } = window.visualViewport;\n        width.value = Math.round(visualViewportWidth * scale);\n        height.value = Math.round(visualViewportHeight * scale);\n      } else if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  const listenerOptions = { passive: true };\n  useEventListener(\"resize\", update, listenerOptions);\n  if (window && type === \"visual\" && window.visualViewport) {\n    useEventListener(window.visualViewport, \"resize\", update, listenerOptions);\n  }\n  if (listenOrientation) {\n    const matches = useMediaQuery(\"(orientation: portrait)\");\n    watch(matches, () => update());\n  }\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsElement, breakpointsMasterCss, breakpointsPrimeFlex, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, breakpointsVuetifyV2, breakpointsVuetifyV3, cloneFnJSON, computedAsync, computedInject, createFetch, createReusableTemplate, createTemplatePromise, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, executeTransition, formatTimeAgo, formatTimeAgoIntl, formatTimeAgoIntlParts, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onElementRemoval, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, provideSSRWidth, setSSRHandler, templateRef, unrefElement, useActiveElement, useAnimate, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useClipboardItems, useCloned, useColorMode, useConfirmDialog, useCountdown, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, useParentElement, usePerformanceObserver, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePreferredReducedTransparency, usePrevious, useRafFn, useRefHistory, useResizeObserver, useSSRWidth, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeAgoIntl, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n", "const defaultTimestep = (1 / 60) * 1000;\nconst getCurrentTime = typeof performance !== \"undefined\"\n    ? () => performance.now()\n    : () => Date.now();\nconst onNextFrame = typeof window !== \"undefined\"\n    ? (callback) => window.requestAnimationFrame(callback)\n    : (callback) => setTimeout(() => callback(getCurrentTime()), defaultTimestep);\n\nexport { defaultTimestep, onNextFrame };\n", "function createRenderStep(runNextFrame) {\n    let toRun = [];\n    let toRunNextFrame = [];\n    let numToRun = 0;\n    let isProcessing = false;\n    let flushNextFrame = false;\n    const toKeepAlive = new WeakSet();\n    const step = {\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const buffer = addToCurrentFrame ? toRun : toRunNextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (buffer.indexOf(callback) === -1) {\n                buffer.push(callback);\n                if (addToCurrentFrame && isProcessing)\n                    numToRun = toRun.length;\n            }\n            return callback;\n        },\n        cancel: (callback) => {\n            const index = toRunNextFrame.indexOf(callback);\n            if (index !== -1)\n                toRunNextFrame.splice(index, 1);\n            toKeepAlive.delete(callback);\n        },\n        process: (frameData) => {\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [toRun, toRunNextFrame] = [toRunNextFrame, toRun];\n            toRunNextFrame.length = 0;\n            numToRun = toRun.length;\n            if (numToRun) {\n                for (let i = 0; i < numToRun; i++) {\n                    const callback = toRun[i];\n                    callback(frameData);\n                    if (toKeepAlive.has(callback)) {\n                        step.schedule(callback);\n                        runNextFrame();\n                    }\n                }\n            }\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n", "import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\n\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst frame = {\n    delta: 0,\n    timestamp: 0,\n};\nconst stepsOrder = [\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => (runNextFrame = true));\n    return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n        if (!runNextFrame)\n            startLoop();\n        return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = steps[key].cancel;\n    return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = () => steps[key].process(frame);\n    return acc;\n}, {});\nconst processStep = (stepId) => steps[stepId].process(frame);\nconst processFrame = (timestamp) => {\n    runNextFrame = false;\n    frame.delta = useDefaultElapsed\n        ? defaultTimestep\n        : Math.max(Math.min(timestamp - frame.timestamp, maxElapsed), 1);\n    frame.timestamp = timestamp;\n    isProcessing = true;\n    stepsOrder.forEach(processStep);\n    isProcessing = false;\n    if (runNextFrame) {\n        useDefaultElapsed = false;\n        onNextFrame(processFrame);\n    }\n};\nconst startLoop = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!isProcessing)\n        onNextFrame(processFrame);\n};\nconst getFrameData = () => frame;\n\nexport default sync;\nexport { cancelSync, flushSync, getFrameData };\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "var warning = function () { };\r\nvar invariant = function () { };\r\nif (process.env.NODE_ENV !== 'production') {\r\n    warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n\nexport { invariant, warning };\n", "const clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\nexport { clamp };\n", "import { warning } from 'hey-listen';\nimport { clamp } from '../../utils/clamp.mjs';\n\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring({ duration = 800, bounce = 0.25, velocity = 0, mass = 1, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= maxDuration * 1000, \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    dampingRatio = clamp(minDamping, maxDamping, dampingRatio);\n    duration = clamp(minDuration, maxDuration, duration / 1000);\n    if (dampingRatio < 1) {\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = duration * 1000;\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: 100,\n            damping: 10,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring, maxDamping, maxDuration, minDamping, minDuration };\n", "import { __rest } from 'tslib';\nimport { findSpring, calcAngularFreq } from '../utils/find-spring.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = Object.assign({ velocity: 0.0, stiffness: 100, damping: 10, mass: 1.0, isResolvedFromDuration: false }, options);\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        const derived = findSpring(options);\n        springOptions = Object.assign(Object.assign(Object.assign({}, springOptions), derived), { velocity: 0.0, mass: 1.0 });\n        springOptions.isResolvedFromDuration = true;\n    }\n    return springOptions;\n}\nfunction spring(_a) {\n    var { from = 0.0, to = 1.0, restSpeed = 2, restDelta } = _a, options = __rest(_a, [\"from\", \"to\", \"restSpeed\", \"restDelta\"]);\n    const state = { done: false, value: from };\n    let { stiffness, damping, mass, velocity, duration, isResolvedFromDuration, } = getSpringOptions(options);\n    let resolveSpring = zero;\n    let resolveVelocity = zero;\n    function createSpring() {\n        const initialVelocity = velocity ? -(velocity / 1000) : 0.0;\n        const initialDelta = to - from;\n        const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n        const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n        if (restDelta === undefined) {\n            restDelta = Math.min(Math.abs(to - from) / 100, 0.4);\n        }\n        if (dampingRatio < 1) {\n            const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n            resolveSpring = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return (to -\n                    envelope *\n                        (((initialVelocity +\n                            dampingRatio * undampedAngularFreq * initialDelta) /\n                            angularFreq) *\n                            Math.sin(angularFreq * t) +\n                            initialDelta * Math.cos(angularFreq * t)));\n            };\n            resolveVelocity = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return (dampingRatio *\n                    undampedAngularFreq *\n                    envelope *\n                    ((Math.sin(angularFreq * t) *\n                        (initialVelocity +\n                            dampingRatio *\n                                undampedAngularFreq *\n                                initialDelta)) /\n                        angularFreq +\n                        initialDelta * Math.cos(angularFreq * t)) -\n                    envelope *\n                        (Math.cos(angularFreq * t) *\n                            (initialVelocity +\n                                dampingRatio *\n                                    undampedAngularFreq *\n                                    initialDelta) -\n                            angularFreq *\n                                initialDelta *\n                                Math.sin(angularFreq * t)));\n            };\n        }\n        else if (dampingRatio === 1) {\n            resolveSpring = (t) => to -\n                Math.exp(-undampedAngularFreq * t) *\n                    (initialDelta +\n                        (initialVelocity + undampedAngularFreq * initialDelta) *\n                            t);\n        }\n        else {\n            const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n            resolveSpring = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                const freqForT = Math.min(dampedAngularFreq * t, 300);\n                return (to -\n                    (envelope *\n                        ((initialVelocity +\n                            dampingRatio * undampedAngularFreq * initialDelta) *\n                            Math.sinh(freqForT) +\n                            dampedAngularFreq *\n                                initialDelta *\n                                Math.cosh(freqForT))) /\n                        dampedAngularFreq);\n            };\n        }\n    }\n    createSpring();\n    return {\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                const currentVelocity = resolveVelocity(t) * 1000;\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(to - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? to : current;\n            return state;\n        },\n        flipTarget: () => {\n            velocity = -velocity;\n            [from, to] = [to, from];\n            createSpring();\n        },\n    };\n}\nspring.needsInterpolation = (a, b) => typeof a === \"string\" || typeof b === \"string\";\nconst zero = (_t) => 0;\n\nexport { spring };\n", "const progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n", "const mix = (from, to, progress) => -progress * from + progress * to + from;\n\nexport { mix };\n", "const clamp = (min, max) => (v) => Math.max(Math.min(v, max), min);\nconst sanitize = (v) => (v % 1 ? Number(v.toFixed(5)) : v);\nconst floatRegex = /(-)?([\\d]*\\.?[\\d])+/g;\nconst colorRegex = /(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))/gi;\nconst singleColorRegex = /^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))$/i;\nfunction isString(v) {\n    return typeof v === 'string';\n}\n\nexport { clamp, colorRegex, floatRegex, isString, sanitize, singleColorRegex };\n", "import { clamp } from '../utils.mjs';\n\nconst number = {\n    test: (v) => typeof v === 'number',\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = Object.assign(Object.assign({}, number), { transform: clamp(0, 1) });\nconst scale = Object.assign(Object.assign({}, number), { default: 1 });\n\nexport { alpha, number, scale };\n", "import { isString } from '../utils.mjs';\n\nconst createUnitType = (unit) => ({\n    test: (v) => isString(v) && v.endsWith(unit) && v.split(' ').length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = createUnitType('deg');\nconst percent = createUnitType('%');\nconst px = createUnitType('px');\nconst vh = createUnitType('vh');\nconst vw = createUnitType('vw');\nconst progressPercentage = Object.assign(Object.assign({}, percent), { parse: (v) => percent.parse(v) / 100, transform: (v) => percent.transform(v * 100) });\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n", "import { isString, singleColorRegex, floatRegex } from '../utils.mjs';\n\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((isString(v) && singleColorRegex.test(v) && v.startsWith(type)) ||\n        (testProp && Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (!isString(v))\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n", "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: isColorString('hsl', 'hue'),\n    parse: splitColor('hue', 'saturation', 'lightness'),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return ('hsla(' +\n            Math.round(hue) +\n            ', ' +\n            percent.transform(sanitize(saturation)) +\n            ', ' +\n            percent.transform(sanitize(lightness)) +\n            ', ' +\n            sanitize(alpha.transform(alpha$1)) +\n            ')');\n    },\n};\n\nexport { hsla };\n", "import { number, alpha } from '../numbers/index.mjs';\nimport { sanitize, clamp } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = clamp(0, 255);\nconst rgbUnit = Object.assign(Object.assign({}, number), { transform: (v) => Math.round(clampRgbUnit(v)) });\nconst rgba = {\n    test: isColorString('rgb', 'red'),\n    parse: splitColor('red', 'green', 'blue'),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => 'rgba(' +\n        rgbUnit.transform(red) +\n        ', ' +\n        rgbUnit.transform(green) +\n        ', ' +\n        rgbUnit.transform(blue) +\n        ', ' +\n        sanitize(alpha.transform(alpha$1)) +\n        ')',\n};\n\nexport { rgbUnit, rgba };\n", "import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = '';\n    let g = '';\n    let b = '';\n    let a = '';\n    if (v.length > 5) {\n        r = v.substr(1, 2);\n        g = v.substr(3, 2);\n        b = v.substr(5, 2);\n        a = v.substr(7, 2);\n    }\n    else {\n        r = v.substr(1, 1);\n        g = v.substr(2, 1);\n        b = v.substr(3, 1);\n        a = v.substr(4, 1);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: isColorString('#'),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n", "import { isString } from '../utils.mjs';\nimport { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return isString(v)\n            ? v\n            : v.hasOwnProperty('red')\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n};\n\nexport { color };\n", "import { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { isString, floatRegex, colorRegex, sanitize } from '../utils.mjs';\n\nconst colorToken = '${c}';\nconst numberToken = '${n}';\nfunction test(v) {\n    var _a, _b, _c, _d;\n    return (isNaN(v) &&\n        isString(v) &&\n        ((_b = (_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = v.match(colorRegex)) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0) > 0);\n}\nfunction analyse(v) {\n    if (typeof v === 'number')\n        v = `${v}`;\n    const values = [];\n    let numColors = 0;\n    const colors = v.match(colorRegex);\n    if (colors) {\n        numColors = colors.length;\n        v = v.replace(colorRegex, colorToken);\n        values.push(...colors.map(color.parse));\n    }\n    const numbers = v.match(floatRegex);\n    if (numbers) {\n        v = v.replace(floatRegex, numberToken);\n        values.push(...numbers.map(number.parse));\n    }\n    return { values, numColors, tokenised: v };\n}\nfunction parse(v) {\n    return analyse(v).values;\n}\nfunction createTransformer(v) {\n    const { values, numColors, tokenised } = analyse(v);\n    const numValues = values.length;\n    return (v) => {\n        let output = tokenised;\n        for (let i = 0; i < numValues; i++) {\n            output = output.replace(i < numColors ? colorToken : numberToken, i < numColors ? color.transform(v[i]) : sanitize(v[i]));\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === 'number' ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parse(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = { test, parse, createTransformer, getAnimatableNone };\n\nexport { complex };\n", "import { complex } from './index.mjs';\nimport { floatRegex } from '../utils.mjs';\n\nconst maxDefaults = new Set(['brightness', 'contrast', 'saturate', 'opacity']);\nfunction applyDefaultFilter(v) {\n    let [name, value] = v.slice(0, -1).split('(');\n    if (name === 'drop-shadow')\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, '');\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + '(' + defaultValue + unit + ')';\n}\nconst functionRegex = /([a-z-]*)\\(.*?\\)/g;\nconst filter = Object.assign(Object.assign({}, complex), { getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(' ') : v;\n    } });\n\nexport { filter };\n", "function hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n", "import { mix } from './mix.mjs';\nimport { hsla, rgba, hex } from 'style-value-types';\nimport { invariant } from 'hey-listen';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\n\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const toExpo = to * to;\n    return Math.sqrt(Math.max(0, v * (toExpo - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nconst notAnimatable = (color) => `'${color}' is not an animatable color. Use the equivalent color code instead.`;\nconst mixColor = (from, to) => {\n    let fromColorType = getColorType(from);\n    let toColorType = getColorType(to);\n    invariant(!!fromColorType, notAnimatable(from));\n    invariant(!!toColorType, notAnimatable(to));\n    let fromColor = fromColorType.parse(from);\n    let toColor = toColorType.parse(to);\n    if (fromColorType === hsla) {\n        fromColor = hslaToRgba(fromColor);\n        fromColorType = rgba;\n    }\n    if (toColorType === hsla) {\n        toColor = hslaToRgba(toColor);\n        toColorType = rgba;\n    }\n    const blended = Object.assign({}, fromColor);\n    return (v) => {\n        for (const key in blended) {\n            if (key !== \"alpha\") {\n                blended[key] = mixLinearColor(fromColor[key], toColor[key], v);\n            }\n        }\n        blended.alpha = mix(fromColor.alpha, toColor.alpha, v);\n        return fromColorType.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n", "const zeroPoint = {\n    x: 0,\n    y: 0,\n    z: 0\n};\nconst isNum = (v) => typeof v === 'number';\n\nexport { isNum, zeroPoint };\n", "const combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n", "import { complex, color } from 'style-value-types';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { isNum } from './inc.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from 'hey-listen';\n\nfunction getMixer(origin, target) {\n    if (isNum(origin)) {\n        return (v) => mix(origin, target, v);\n    }\n    else if (color.test(origin)) {\n        return mixColor(origin, target);\n    }\n    else {\n        return mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to) => {\n    const output = [...from];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n    return (v) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target) => {\n    const output = Object.assign(Object.assign({}, origin), target);\n    const blendValue = {};\n    for (const key in output) {\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nfunction analyse(value) {\n    const parsed = complex.parse(value);\n    const numValues = parsed.length;\n    let numNumbers = 0;\n    let numRGB = 0;\n    let numHSL = 0;\n    for (let i = 0; i < numValues; i++) {\n        if (numNumbers || typeof parsed[i] === \"number\") {\n            numNumbers++;\n        }\n        else {\n            if (parsed[i].hue !== undefined) {\n                numHSL++;\n            }\n            else {\n                numRGB++;\n            }\n        }\n    }\n    return { parsed, numNumbers, numRGB, numHSL };\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyse(origin);\n    const targetStats = analyse(target);\n    const canInterpolate = originStats.numHSL === targetStats.numHSL &&\n        originStats.numRGB === targetStats.numRGB &&\n        originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return pipe(mixArray(originStats.parsed, targetStats.parsed), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return (p) => `${p > 0 ? target : origin}`;\n    }\n};\n\nexport { mixArray, mixComplex, mixObject };\n", "import { progress } from './progress.mjs';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { mixComplex, mixArray, mixObject } from './mix-complex.mjs';\nimport { color } from 'style-value-types';\nimport { clamp } from './clamp.mjs';\nimport { pipe } from './pipe.mjs';\nimport { invariant } from 'hey-listen';\n\nconst mixNumber = (from, to) => (p) => mix(from, to, p);\nfunction detectMixerFactory(v) {\n    if (typeof v === 'number') {\n        return mixNumber;\n    }\n    else if (typeof v === 'string') {\n        if (color.test(v)) {\n            return mixColor;\n        }\n        else {\n            return mixComplex;\n        }\n    }\n    else if (Array.isArray(v)) {\n        return mixArray;\n    }\n    else if (typeof v === 'object') {\n        return mixObject;\n    }\n}\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || detectMixerFactory(output[0]);\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\nfunction fastInterpolate([from, to], [mixer]) {\n    return (v) => mixer(progress(from, to, v));\n}\nfunction slowInterpolate(input, mixers) {\n    const inputLength = input.length;\n    const lastInputIndex = inputLength - 1;\n    return (v) => {\n        let mixerIndex = 0;\n        let foundMixerIndex = false;\n        if (v <= input[0]) {\n            foundMixerIndex = true;\n        }\n        else if (v >= input[lastInputIndex]) {\n            mixerIndex = lastInputIndex - 1;\n            foundMixerIndex = true;\n        }\n        if (!foundMixerIndex) {\n            let i = 1;\n            for (; i < inputLength; i++) {\n                if (input[i] > v || i === lastInputIndex) {\n                    break;\n                }\n            }\n            mixerIndex = i - 1;\n        }\n        const progressInRange = progress(input[mixerIndex], input[mixerIndex + 1], v);\n        return mixers[mixerIndex](progressInRange);\n    };\n}\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, 'Both input and output ranges must be the same length');\n    invariant(!ease || !Array.isArray(ease) || ease.length === inputLength - 1, 'Array of easing functions must be of length `input.length - 1`, as it applies to the transitions **between** the defined values.');\n    if (input[0] > input[inputLength - 1]) {\n        input = [].concat(input);\n        output = [].concat(output);\n        input.reverse();\n        output.reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const interpolator = inputLength === 2\n        ? fastInterpolate(input, mixers)\n        : slowInterpolate(input, mixers);\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n", "const reverseEasing = easing => p => 1 - easing(1 - p);\nconst mirrorEasing = easing => p => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\nconst createExpoIn = (power) => p => Math.pow(p, power);\nconst createBackIn = (power) => p => p * p * ((power + 1) * p - power);\nconst createAnticipate = (power) => {\n    const backEasing = createBackIn(power);\n    return p => (p *= 2) < 1\n        ? 0.5 * backEasing(p)\n        : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n};\n\nexport { createAnticipate, createBackIn, createExpoIn, mirrorEasing, reverseEasing };\n", "import { createExpoIn, reverseEasing, mirrorEasing, createBackIn, createAnticipate } from './utils.mjs';\n\nconst DEFAULT_OVERSHOOT_STRENGTH = 1.525;\nconst BOUNCE_FIRST_THRESHOLD = 4.0 / 11.0;\nconst BOUNCE_SECOND_THRESHOLD = 8.0 / 11.0;\nconst BOUNCE_THIRD_THRESHOLD = 9.0 / 10.0;\nconst linear = p => p;\nconst easeIn = createExpoIn(2);\nconst easeOut = reverseEasing(easeIn);\nconst easeInOut = mirrorEasing(easeIn);\nconst circIn = p => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circOut);\nconst backIn = createBackIn(DEFAULT_OVERSHOOT_STRENGTH);\nconst backOut = reverseEasing(backIn);\nconst backInOut = mirrorEasing(backIn);\nconst anticipate = createAnticipate(DEFAULT_OVERSHOOT_STRENGTH);\nconst ca = 4356.0 / 361.0;\nconst cb = 35442.0 / 1805.0;\nconst cc = 16061.0 / 1805.0;\nconst bounceOut = (p) => {\n    if (p === 1 || p === 0)\n        return p;\n    const p2 = p * p;\n    return p < BOUNCE_FIRST_THRESHOLD\n        ? 7.5625 * p2\n        : p < BOUNCE_SECOND_THRESHOLD\n            ? 9.075 * p2 - 9.9 * p + 3.4\n            : p < BOUNCE_THIRD_THRESHOLD\n                ? ca * p2 - cb * p + cc\n                : 10.8 * p * p - 20.52 * p + 10.72;\n};\nconst bounceIn = reverseEasing(bounceOut);\nconst bounceInOut = (p) => p < 0.5\n    ? 0.5 * (1.0 - bounceOut(1.0 - p * 2.0))\n    : 0.5 * bounceOut(p * 2.0 - 1.0) + 0.5;\n\nexport { anticipate, backIn, backInOut, backOut, bounceIn, bounceInOut, bounceOut, circIn, circInOut, circOut, easeIn, easeInOut, easeOut, linear };\n", "import { interpolate } from '../../utils/interpolate.mjs';\nimport { easeInOut } from '../../easing/index.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction defaultOffset(values) {\n    const numValues = values.length;\n    return values.map((_value, i) => i !== 0 ? i / (numValues - 1) : 0);\n}\nfunction convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\nfunction keyframes({ from = 0, to = 1, ease, offset, duration = 300, }) {\n    const state = { done: false, value: from };\n    const values = Array.isArray(to) ? to : [from, to];\n    const times = convertOffsetToTimes(offset && offset.length === values.length\n        ? offset\n        : defaultOffset(values), duration);\n    function createInterpolator() {\n        return interpolate(times, values, {\n            ease: Array.isArray(ease) ? ease : defaultEasing(values, ease),\n        });\n    }\n    let interpolator = createInterpolator();\n    return {\n        next: (t) => {\n            state.value = interpolator(t);\n            state.done = t >= duration;\n            return state;\n        },\n        flipTarget: () => {\n            values.reverse();\n            interpolator = createInterpolator();\n        },\n    };\n}\n\nexport { convertOffsetToTimes, defaultEasing, defaultOffset, keyframes };\n", "function decay({ velocity = 0, from = 0, power = 0.8, timeConstant = 350, restDelta = 0.5, modifyTarget, }) {\n    const state = { done: false, value: from };\n    let amplitude = power * velocity;\n    const ideal = from + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    if (target !== ideal)\n        amplitude = target - from;\n    return {\n        next: (t) => {\n            const delta = -amplitude * Math.exp(-t / timeConstant);\n            state.done = !(delta > restDelta || delta < -restDelta);\n            state.value = state.done ? target : target + delta;\n            return state;\n        },\n        flipTarget: () => { },\n    };\n}\n\nexport { decay };\n", "import { spring } from '../generators/spring.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { decay } from '../generators/decay.mjs';\n\nconst types = { keyframes, spring, decay };\nfunction detectAnimationFromOptions(config) {\n    if (Array.isArray(config.to)) {\n        return keyframes;\n    }\n    else if (types[config.type]) {\n        return types[config.type];\n    }\n    const keys = new Set(Object.keys(config));\n    if (keys.has(\"ease\") ||\n        (keys.has(\"duration\") && !keys.has(\"dampingRatio\"))) {\n        return keyframes;\n    }\n    else if (keys.has(\"dampingRatio\") ||\n        keys.has(\"stiffness\") ||\n        keys.has(\"mass\") ||\n        keys.has(\"damping\") ||\n        keys.has(\"restSpeed\") ||\n        keys.has(\"restDelta\")) {\n        return spring;\n    }\n    return keyframes;\n}\n\nexport { detectAnimationFromOptions };\n", "function loopElapsed(elapsed, duration, delay = 0) {\n    return elapsed - duration - delay;\n}\nfunction reverseElapsed(elapsed, duration, delay = 0, isForwardPlayback = true) {\n    return isForwardPlayback\n        ? loopElapsed(duration + -elapsed, duration, delay)\n        : duration - (elapsed - duration) + delay;\n}\nfunction hasRepeatDelayElapsed(elapsed, duration, delay, isForwardPlayback) {\n    return isForwardPlayback ? elapsed >= duration + delay : elapsed <= -delay;\n}\n\nexport { hasRepeatDelayElapsed, loopElapsed, reverseElapsed };\n", "import { __rest } from 'tslib';\nimport { detectAnimationFromOptions } from './utils/detect-animation-from-options.mjs';\nimport sync, { cancelSync } from 'framesync';\nimport { interpolate } from '../utils/interpolate.mjs';\nimport { hasRepeatDelayElapsed, reverseElapsed, loopElapsed } from './utils/elapsed.mjs';\n\nconst framesync = (update) => {\n    const passTimestamp = ({ delta }) => update(delta);\n    return {\n        start: () => sync.update(passTimestamp, true),\n        stop: () => cancelSync.update(passTimestamp),\n    };\n};\nfunction animate(_a) {\n    var _b, _c;\n    var { from, autoplay = true, driver = framesync, elapsed = 0, repeat: repeatMax = 0, repeatType = \"loop\", repeatDelay = 0, onPlay, onStop, onComplete, onRepeat, onUpdate } = _a, options = __rest(_a, [\"from\", \"autoplay\", \"driver\", \"elapsed\", \"repeat\", \"repeatType\", \"repeatDelay\", \"onPlay\", \"onStop\", \"onComplete\", \"onRepeat\", \"onUpdate\"]);\n    let { to } = options;\n    let driverControls;\n    let repeatCount = 0;\n    let computedDuration = options.duration;\n    let latest;\n    let isComplete = false;\n    let isForwardPlayback = true;\n    let interpolateFromNumber;\n    const animator = detectAnimationFromOptions(options);\n    if ((_c = (_b = animator).needsInterpolation) === null || _c === void 0 ? void 0 : _c.call(_b, from, to)) {\n        interpolateFromNumber = interpolate([0, 100], [from, to], {\n            clamp: false,\n        });\n        from = 0;\n        to = 100;\n    }\n    const animation = animator(Object.assign(Object.assign({}, options), { from, to }));\n    function repeat() {\n        repeatCount++;\n        if (repeatType === \"reverse\") {\n            isForwardPlayback = repeatCount % 2 === 0;\n            elapsed = reverseElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback);\n        }\n        else {\n            elapsed = loopElapsed(elapsed, computedDuration, repeatDelay);\n            if (repeatType === \"mirror\")\n                animation.flipTarget();\n        }\n        isComplete = false;\n        onRepeat && onRepeat();\n    }\n    function complete() {\n        driverControls.stop();\n        onComplete && onComplete();\n    }\n    function update(delta) {\n        if (!isForwardPlayback)\n            delta = -delta;\n        elapsed += delta;\n        if (!isComplete) {\n            const state = animation.next(Math.max(0, elapsed));\n            latest = state.value;\n            if (interpolateFromNumber)\n                latest = interpolateFromNumber(latest);\n            isComplete = isForwardPlayback ? state.done : elapsed <= 0;\n        }\n        onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(latest);\n        if (isComplete) {\n            if (repeatCount === 0)\n                computedDuration !== null && computedDuration !== void 0 ? computedDuration : (computedDuration = elapsed);\n            if (repeatCount < repeatMax) {\n                hasRepeatDelayElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback) && repeat();\n            }\n            else {\n                complete();\n            }\n        }\n    }\n    function play() {\n        onPlay === null || onPlay === void 0 ? void 0 : onPlay();\n        driverControls = driver(update);\n        driverControls.start();\n    }\n    autoplay && play();\n    return {\n        stop: () => {\n            onStop === null || onStop === void 0 ? void 0 : onStop();\n            driverControls.stop();\n        },\n    };\n}\n\nexport { animate };\n", "function velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n", "import { animate } from './index.mjs';\nimport { velocityPerSecond } from '../utils/velocity-per-second.mjs';\nimport { getFrameData } from 'framesync';\n\nfunction inertia({ from = 0, velocity = 0, min, max, power = 0.8, timeConstant = 750, bounceStiffness = 500, bounceDamping = 10, restDelta = 1, modifyTarget, driver, onUpdate, onComplete, onStop, }) {\n    let currentAnimation;\n    function isOutOfBounds(v) {\n        return (min !== undefined && v < min) || (max !== undefined && v > max);\n    }\n    function boundaryNearest(v) {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    }\n    function startAnimation(options) {\n        currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop();\n        currentAnimation = animate(Object.assign(Object.assign({}, options), { driver, onUpdate: (v) => {\n                var _a;\n                onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(v);\n                (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, v);\n            }, onComplete,\n            onStop }));\n    }\n    function startSpring(options) {\n        startAnimation(Object.assign({ type: \"spring\", stiffness: bounceStiffness, damping: bounceDamping, restDelta }, options));\n    }\n    if (isOutOfBounds(from)) {\n        startSpring({ from, velocity, to: boundaryNearest(from) });\n    }\n    else {\n        let target = power * velocity + from;\n        if (typeof modifyTarget !== \"undefined\")\n            target = modifyTarget(target);\n        const boundary = boundaryNearest(target);\n        const heading = boundary === min ? -1 : 1;\n        let prev;\n        let current;\n        const checkBoundary = (v) => {\n            prev = current;\n            current = v;\n            velocity = velocityPerSecond(v - prev, getFrameData().delta);\n            if ((heading === 1 && v > boundary) ||\n                (heading === -1 && v < boundary)) {\n                startSpring({ from: v, to: boundary, velocity });\n            }\n        };\n        startAnimation({\n            type: \"decay\",\n            from,\n            velocity,\n            timeConstant,\n            power,\n            restDelta,\n            modifyTarget,\n            onUpdate: isOutOfBounds(target) ? checkBoundary : undefined,\n        });\n    }\n    return {\n        stop: () => currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop(),\n    };\n}\n\nexport { inertia };\n", "const identity = (v) => v;\nconst createAttractor = (alterDisplacement = identity) => (constant, origin, v) => {\n    const displacement = origin - v;\n    const springModifiedDisplacement = -(0 - constant + 1) * (0 - alterDisplacement(Math.abs(displacement)));\n    return displacement <= 0\n        ? origin + springModifiedDisplacement\n        : origin - springModifiedDisplacement;\n};\nconst attract = createAttractor();\nconst attractExpo = createAttractor(Math.sqrt);\n\nexport { attract, attractExpo, createAttractor };\n", "import { linear } from './index.mjs';\n\nconst a = (a1, a2) => 1.0 - 3.0 * a2 + 3.0 * a1;\nconst b = (a1, a2) => 3.0 * a2 - 6.0 * a1;\nconst c = (a1) => 3.0 * a1;\nconst calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\nconst getSlope = (t, a1, a2) => 3.0 * a(a1, a2) * t * t + 2.0 * b(a1, a2) * t + c(a1);\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 10;\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = aA + (aB - aA) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - aX;\n        if (currentX > 0.0) {\n            aB = currentT;\n        }\n        else {\n            aA = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nconst newtonIterations = 8;\nconst newtonMinSlope = 0.001;\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n    for (let i = 0; i < newtonIterations; ++i) {\n        const currentSlope = getSlope(aGuessT, mX1, mX2);\n        if (currentSlope === 0.0) {\n            return aGuessT;\n        }\n        const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n        aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n}\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    if (mX1 === mY1 && mX2 === mY2)\n        return linear;\n    const sampleValues = new Float32Array(kSplineTableSize);\n    for (let i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n    function getTForX(aX) {\n        let intervalStart = 0.0;\n        let currentSample = 1;\n        const lastSample = kSplineTableSize - 1;\n        for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n            intervalStart += kSampleStepSize;\n        }\n        --currentSample;\n        const dist = (aX - sampleValues[currentSample]) /\n            (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n        const guessForT = intervalStart + dist * kSampleStepSize;\n        const initialSlope = getSlope(guessForT, mX1, mX2);\n        if (initialSlope >= newtonMinSlope) {\n            return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n        }\n        else if (initialSlope === 0.0) {\n            return guessForT;\n        }\n        else {\n            return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n        }\n    }\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n", "import defu from 'defu';\nimport { ref, unref, watch, computed, reactive, toRaw, inject, onUpdated, nextTick, defineComponent, useSlots, h, Fragment, isRef } from 'vue';\nimport { isObject as isObject$1, useEventListener, useIntersectionObserver, unrefElement, useMediaQuery } from '@vueuse/core';\nimport { tryOnUnmounted } from '@vueuse/shared';\nimport sync, { getFrameData } from 'framesync';\nimport { velocityPerSecond, inertia, animate, cubicBezier, bounceOut, bounceInOut, bounceIn, anticipate, backOut, backInOut, backIn, circOut, circInOut, circIn, easeOut, easeInOut, easeIn, linear } from 'popmotion';\nimport { number, alpha, filter, px, progressPercentage, degrees, scale, color, complex } from 'style-value-types';\n\nconst motionState = {};\n\nclass SubscriptionManager {\n  subscriptions = /* @__PURE__ */ new Set();\n  add(handler) {\n    this.subscriptions.add(handler);\n    return () => this.subscriptions.delete(handler);\n  }\n  notify(a, b, c) {\n    if (!this.subscriptions.size)\n      return;\n    for (const handler of this.subscriptions) handler(a, b, c);\n  }\n  clear() {\n    this.subscriptions.clear();\n  }\n}\n\nfunction isFloat(value) {\n  return !Number.isNaN(Number.parseFloat(value));\n}\nclass MotionValue {\n  /**\n   * The current state of the `MotionValue`.\n   */\n  current;\n  /**\n   * The previous state of the `MotionValue`.\n   */\n  prev;\n  /**\n   * Duration, in milliseconds, since last updating frame.\n   */\n  timeDelta = 0;\n  /**\n   * Timestamp of the last time this `MotionValue` was updated.\n   */\n  lastUpdated = 0;\n  /**\n   * Functions to notify when the `MotionValue` updates.\n   */\n  updateSubscribers = new SubscriptionManager();\n  /**\n   * A reference to the currently-controlling Popmotion animation\n   */\n  stopAnimation;\n  /**\n   * Tracks whether this value can output a velocity.\n   */\n  canTrackVelocity = false;\n  /**\n   * init - The initiating value\n   * config - Optional configuration options\n   */\n  constructor(init) {\n    this.prev = this.current = init;\n    this.canTrackVelocity = isFloat(this.current);\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   */\n  onChange(subscription) {\n    return this.updateSubscribers.add(subscription);\n  }\n  clearListeners() {\n    this.updateSubscribers.clear();\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @param v\n   * @param render\n   */\n  set(v) {\n    this.updateAndNotify(v);\n  }\n  /**\n   * Update and notify `MotionValue` subscribers.\n   *\n   * @param v\n   * @param render\n   */\n  updateAndNotify = (v) => {\n    this.prev = this.current;\n    this.current = v;\n    const { delta, timestamp } = getFrameData();\n    if (this.lastUpdated !== timestamp) {\n      this.timeDelta = delta;\n      this.lastUpdated = timestamp;\n    }\n    sync.postRender(this.scheduleVelocityCheck);\n    this.updateSubscribers.notify(this.current);\n  };\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   */\n  get() {\n    return this.current;\n  }\n  /**\n   * Get previous value.\n   *\n   * @returns - The previous latest state of `MotionValue`\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   */\n  getVelocity() {\n    return this.canTrackVelocity ? velocityPerSecond(Number.parseFloat(this.current) - Number.parseFloat(this.prev), this.timeDelta) : 0;\n  }\n  /**\n   * Schedule a velocity check for the next frame.\n   */\n  scheduleVelocityCheck = () => sync.postRender(this.velocityCheck);\n  /**\n   * Updates `prev` with `current` if the value hasn't been updated this frame.\n   * This ensures velocity calculations return `0`.\n   */\n  velocityCheck = ({ timestamp }) => {\n    if (!this.canTrackVelocity)\n      this.canTrackVelocity = isFloat(this.current);\n    if (timestamp !== this.lastUpdated)\n      this.prev = this.current;\n  };\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   */\n  start(animation) {\n    this.stop();\n    return new Promise((resolve) => {\n      const { stop } = animation(resolve);\n      this.stopAnimation = stop;\n    }).then(() => this.clearAnimation());\n  }\n  /**\n   * Stop the currently active animation.\n   */\n  stop() {\n    if (this.stopAnimation)\n      this.stopAnimation();\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   */\n  isAnimating() {\n    return !!this.stopAnimation;\n  }\n  /**\n   * Clear the current animation reference.\n   */\n  clearAnimation() {\n    this.stopAnimation = null;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   */\n  destroy() {\n    this.updateSubscribers.clear();\n    this.stop();\n  }\n}\nfunction getMotionValue(init) {\n  return new MotionValue(init);\n}\n\nconst { isArray } = Array;\nfunction useMotionValues() {\n  const motionValues = ref({});\n  const stop = (keys) => {\n    const destroyKey = (key) => {\n      if (!motionValues.value[key])\n        return;\n      motionValues.value[key].stop();\n      motionValues.value[key].destroy();\n      delete motionValues.value[key];\n    };\n    if (keys) {\n      if (isArray(keys)) {\n        keys.forEach(destroyKey);\n      } else {\n        destroyKey(keys);\n      }\n    } else {\n      Object.keys(motionValues.value).forEach(destroyKey);\n    }\n  };\n  const get = (key, from, target) => {\n    if (motionValues.value[key])\n      return motionValues.value[key];\n    const motionValue = getMotionValue(from);\n    motionValue.onChange((v) => target[key] = v);\n    motionValues.value[key] = motionValue;\n    return motionValue;\n  };\n  tryOnUnmounted(stop);\n  return {\n    motionValues,\n    get,\n    stop\n  };\n}\n\nfunction isKeyframesTarget(v) {\n  return Array.isArray(v);\n}\nfunction underDampedSpring() {\n  return {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restDelta: 0.5,\n    restSpeed: 10\n  };\n}\nfunction criticallyDampedSpring(to) {\n  return {\n    type: \"spring\",\n    stiffness: 550,\n    damping: to === 0 ? 2 * Math.sqrt(550) : 30,\n    restDelta: 0.01,\n    restSpeed: 10\n  };\n}\nfunction overDampedSpring(to) {\n  return {\n    type: \"spring\",\n    stiffness: 550,\n    damping: to === 0 ? 100 : 30,\n    restDelta: 0.01,\n    restSpeed: 10\n  };\n}\nfunction linearTween() {\n  return {\n    type: \"keyframes\",\n    ease: \"linear\",\n    duration: 300\n  };\n}\nfunction keyframes(values) {\n  return {\n    type: \"keyframes\",\n    duration: 800,\n    values\n  };\n}\nconst defaultTransitions = {\n  default: overDampedSpring,\n  x: underDampedSpring,\n  y: underDampedSpring,\n  z: underDampedSpring,\n  rotate: underDampedSpring,\n  rotateX: underDampedSpring,\n  rotateY: underDampedSpring,\n  rotateZ: underDampedSpring,\n  scaleX: criticallyDampedSpring,\n  scaleY: criticallyDampedSpring,\n  scale: criticallyDampedSpring,\n  backgroundColor: linearTween,\n  color: linearTween,\n  opacity: linearTween\n};\nfunction getDefaultTransition(valueKey, to) {\n  let transitionFactory;\n  if (isKeyframesTarget(to)) {\n    transitionFactory = keyframes;\n  } else {\n    transitionFactory = defaultTransitions[valueKey] || defaultTransitions.default;\n  }\n  return { to, ...transitionFactory(to) };\n}\n\nconst int = {\n  ...number,\n  transform: Math.round\n};\nconst valueTypes = {\n  // Color props\n  color,\n  backgroundColor: color,\n  outlineColor: color,\n  fill: color,\n  stroke: color,\n  // Border props\n  borderColor: color,\n  borderTopColor: color,\n  borderRightColor: color,\n  borderBottomColor: color,\n  borderLeftColor: color,\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  size: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Transform props\n  rotate: degrees,\n  rotateX: degrees,\n  rotateY: degrees,\n  rotateZ: degrees,\n  scale,\n  scaleX: scale,\n  scaleY: scale,\n  scaleZ: scale,\n  skew: degrees,\n  skewX: degrees,\n  skewY: degrees,\n  distance: px,\n  translateX: px,\n  translateY: px,\n  translateZ: px,\n  x: px,\n  y: px,\n  z: px,\n  perspective: px,\n  transformPerspective: px,\n  opacity: alpha,\n  originX: progressPercentage,\n  originY: progressPercentage,\n  originZ: px,\n  // Misc\n  zIndex: int,\n  filter,\n  WebkitFilter: filter,\n  // SVG\n  fillOpacity: alpha,\n  strokeOpacity: alpha,\n  numOctaves: int\n};\nconst getValueType = (key) => valueTypes[key];\nfunction getValueAsType(value, type) {\n  return type && typeof value === \"number\" && type.transform ? type.transform(value) : value;\n}\nfunction getAnimatableNone(key, value) {\n  let defaultValueType = getValueType(key);\n  if (defaultValueType !== filter)\n    defaultValueType = complex;\n  return defaultValueType.getAnimatableNone ? defaultValueType.getAnimatableNone(value) : void 0;\n}\n\nconst easingLookup = {\n  linear,\n  easeIn,\n  easeInOut,\n  easeOut,\n  circIn,\n  circInOut,\n  circOut,\n  backIn,\n  backInOut,\n  backOut,\n  anticipate,\n  bounceIn,\n  bounceInOut,\n  bounceOut\n};\nfunction easingDefinitionToFunction(definition) {\n  if (Array.isArray(definition)) {\n    const [x1, y1, x2, y2] = definition;\n    return cubicBezier(x1, y1, x2, y2);\n  } else if (typeof definition === \"string\") {\n    return easingLookup[definition];\n  }\n  return definition;\n}\nfunction isEasingArray(ease) {\n  return Array.isArray(ease) && typeof ease[0] !== \"number\";\n}\nfunction isAnimatable(key, value) {\n  if (key === \"zIndex\")\n    return false;\n  if (typeof value === \"number\" || Array.isArray(value))\n    return true;\n  if (typeof value === \"string\" && complex.test(value) && !value.startsWith(\"url(\")) {\n    return true;\n  }\n  return false;\n}\nfunction hydrateKeyframes(options) {\n  if (Array.isArray(options.to) && options.to[0] === null) {\n    options.to = [...options.to];\n    options.to[0] = options.from;\n  }\n  return options;\n}\nfunction convertTransitionToAnimationOptions({ ease, times, delay, ...transition }) {\n  const options = { ...transition };\n  if (times)\n    options.offset = times;\n  if (ease) {\n    options.ease = isEasingArray(ease) ? ease.map(easingDefinitionToFunction) : easingDefinitionToFunction(ease);\n  }\n  if (delay)\n    options.elapsed = -delay;\n  return options;\n}\nfunction getPopmotionAnimationOptions(transition, options, key) {\n  if (Array.isArray(options.to)) {\n    if (!transition.duration)\n      transition.duration = 800;\n  }\n  hydrateKeyframes(options);\n  if (!isTransitionDefined(transition)) {\n    transition = {\n      ...transition,\n      ...getDefaultTransition(key, options.to)\n    };\n  }\n  return {\n    ...options,\n    ...convertTransitionToAnimationOptions(transition)\n  };\n}\nfunction isTransitionDefined({ delay, repeat, repeatType, repeatDelay, from, ...transition }) {\n  return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] || transition.default || transition;\n}\nfunction getAnimation(key, value, target, transition, onComplete) {\n  const valueTransition = getValueTransition(transition, key);\n  let origin = valueTransition.from === null || valueTransition.from === void 0 ? value.get() : valueTransition.from;\n  const isTargetAnimatable = isAnimatable(key, target);\n  if (origin === \"none\" && isTargetAnimatable && typeof target === \"string\")\n    origin = getAnimatableNone(key, target);\n  const isOriginAnimatable = isAnimatable(key, origin);\n  function start(complete) {\n    const options = {\n      from: origin,\n      to: target,\n      velocity: transition.velocity ? transition.velocity : value.getVelocity(),\n      onUpdate: (v) => value.set(v)\n    };\n    return valueTransition.type === \"inertia\" || valueTransition.type === \"decay\" ? inertia({ ...options, ...valueTransition }) : animate({\n      ...getPopmotionAnimationOptions(valueTransition, options, key),\n      onUpdate: (v) => {\n        options.onUpdate(v);\n        if (valueTransition.onUpdate)\n          valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        if (onComplete)\n          onComplete();\n        if (complete)\n          complete();\n      }\n    });\n  }\n  function set(complete) {\n    value.set(target);\n    if (onComplete)\n      onComplete();\n    if (complete)\n      complete();\n    return { stop: () => {\n    } };\n  }\n  return !isOriginAnimatable || !isTargetAnimatable || valueTransition.type === false ? set : start;\n}\n\nfunction useMotionTransitions() {\n  const { motionValues, stop, get } = useMotionValues();\n  const push = (key, value, target, transition = {}, onComplete) => {\n    const from = target[key];\n    const motionValue = get(key, from, target);\n    if (transition && transition.immediate) {\n      motionValue.set(value);\n      return;\n    }\n    const animation = getAnimation(key, motionValue, value, transition, onComplete);\n    motionValue.start(animation);\n  };\n  return { motionValues, stop, push };\n}\n\nfunction useMotionControls(motionProperties, variants = {}, { motionValues, push, stop } = useMotionTransitions()) {\n  const _variants = unref(variants);\n  const isAnimating = ref(false);\n  watch(\n    motionValues,\n    (newVal) => {\n      isAnimating.value = Object.values(newVal).filter((value) => value.isAnimating()).length > 0;\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  const getVariantFromKey = (variant) => {\n    if (!_variants || !_variants[variant])\n      throw new Error(`The variant ${variant} does not exist.`);\n    return _variants[variant];\n  };\n  const apply = (variant) => {\n    if (typeof variant === \"string\")\n      variant = getVariantFromKey(variant);\n    const animations = Object.entries(variant).map(([key, value]) => {\n      if (key === \"transition\")\n        return void 0;\n      return new Promise(\n        (resolve) => (\n          // @ts-expect-error - Fix errors later for typescript 5\n          push(key, value, motionProperties, variant.transition || getDefaultTransition(key, variant[key]), resolve)\n        )\n      );\n    }).filter(Boolean);\n    async function waitForComplete() {\n      await Promise.all(animations);\n      variant.transition?.onComplete?.();\n    }\n    return Promise.all([waitForComplete()]);\n  };\n  const set = (variant) => {\n    const variantData = isObject$1(variant) ? variant : getVariantFromKey(variant);\n    Object.entries(variantData).forEach(([key, value]) => {\n      if (key === \"transition\")\n        return;\n      push(key, value, motionProperties, {\n        immediate: true\n      });\n    });\n  };\n  const leave = async (done) => {\n    let leaveVariant;\n    if (_variants) {\n      if (_variants.leave)\n        leaveVariant = _variants.leave;\n      if (!_variants.leave && _variants.initial)\n        leaveVariant = _variants.initial;\n    }\n    if (!leaveVariant) {\n      done();\n      return;\n    }\n    await apply(leaveVariant);\n    done();\n  };\n  return {\n    isAnimating,\n    apply,\n    set,\n    leave,\n    stop\n  };\n}\n\nconst isBrowser = typeof window !== \"undefined\";\nconst supportsPointerEvents = () => isBrowser && (window.onpointerdown === null || import.meta.env?.TEST);\nconst supportsTouchEvents = () => isBrowser && (window.ontouchstart === null || import.meta.env?.TEST);\nconst supportsMouseEvents = () => isBrowser && (window.onmousedown === null || import.meta.env?.TEST);\n\nfunction registerEventListeners({ target, state, variants, apply }) {\n  const _variants = unref(variants);\n  const hovered = ref(false);\n  const tapped = ref(false);\n  const focused = ref(false);\n  const mutableKeys = computed(() => {\n    let result = [...Object.keys(state.value || {})];\n    if (!_variants)\n      return result;\n    if (_variants.hovered)\n      result = [...result, ...Object.keys(_variants.hovered)];\n    if (_variants.tapped)\n      result = [...result, ...Object.keys(_variants.tapped)];\n    if (_variants.focused)\n      result = [...result, ...Object.keys(_variants.focused)];\n    return result;\n  });\n  const computedProperties = computed(() => {\n    const result = {};\n    Object.assign(result, state.value);\n    if (hovered.value && _variants.hovered)\n      Object.assign(result, _variants.hovered);\n    if (tapped.value && _variants.tapped)\n      Object.assign(result, _variants.tapped);\n    if (focused.value && _variants.focused)\n      Object.assign(result, _variants.focused);\n    for (const key in result) {\n      if (!mutableKeys.value.includes(key))\n        delete result[key];\n    }\n    return result;\n  });\n  if (_variants.hovered) {\n    useEventListener(target, \"mouseenter\", () => hovered.value = true);\n    useEventListener(target, \"mouseleave\", () => {\n      hovered.value = false;\n      tapped.value = false;\n    });\n  }\n  if (_variants.tapped) {\n    if (supportsMouseEvents()) {\n      useEventListener(target, \"mousedown\", () => tapped.value = true);\n      useEventListener(target, \"mouseup\", () => tapped.value = false);\n    }\n    if (supportsPointerEvents()) {\n      useEventListener(target, \"pointerdown\", () => tapped.value = true);\n      useEventListener(target, \"pointerup\", () => tapped.value = false);\n    }\n    if (supportsTouchEvents()) {\n      useEventListener(target, \"touchstart\", () => tapped.value = true);\n      useEventListener(target, \"touchend\", () => tapped.value = false);\n    }\n  }\n  if (_variants.focused) {\n    useEventListener(target, \"focus\", () => focused.value = true);\n    useEventListener(target, \"blur\", () => focused.value = false);\n  }\n  watch([hovered, tapped, focused], () => {\n    apply(computedProperties.value);\n  });\n}\n\nfunction registerLifeCycleHooks({ set, target, variants, variant }) {\n  const _variants = unref(variants);\n  watch(\n    () => target,\n    () => {\n      if (!_variants)\n        return;\n      if (_variants.initial) {\n        set(\"initial\");\n        variant.value = \"initial\";\n      }\n      if (_variants.enter)\n        variant.value = \"enter\";\n    },\n    {\n      immediate: true,\n      flush: \"pre\"\n    }\n  );\n}\n\nfunction registerVariantsSync({ state, apply }) {\n  watch(\n    state,\n    (newVal) => {\n      if (newVal)\n        apply(newVal);\n    },\n    {\n      immediate: true\n    }\n  );\n}\n\nfunction registerVisibilityHooks({ target, variants, variant }) {\n  const _variants = unref(variants);\n  if (_variants && (_variants.visible || _variants.visibleOnce)) {\n    useIntersectionObserver(target, ([{ isIntersecting }]) => {\n      if (_variants.visible) {\n        if (isIntersecting)\n          variant.value = \"visible\";\n        else variant.value = \"initial\";\n      } else if (_variants.visibleOnce) {\n        if (isIntersecting && variant.value !== \"visibleOnce\")\n          variant.value = \"visibleOnce\";\n        else if (!variant.value)\n          variant.value = \"initial\";\n      }\n    });\n  }\n}\n\nfunction useMotionFeatures(instance, options = {\n  syncVariants: true,\n  lifeCycleHooks: true,\n  visibilityHooks: true,\n  eventListeners: true\n}) {\n  if (options.lifeCycleHooks)\n    registerLifeCycleHooks(instance);\n  if (options.syncVariants)\n    registerVariantsSync(instance);\n  if (options.visibilityHooks)\n    registerVisibilityHooks(instance);\n  if (options.eventListeners)\n    registerEventListeners(instance);\n}\n\nfunction reactiveStyle(props = {}) {\n  const state = reactive({\n    ...props\n  });\n  const style = ref({});\n  watch(\n    state,\n    () => {\n      const result = {};\n      for (const [key, value] of Object.entries(state)) {\n        const valueType = getValueType(key);\n        const valueAsType = getValueAsType(value, valueType);\n        result[key] = valueAsType;\n      }\n      style.value = result;\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  return {\n    state,\n    style\n  };\n}\n\nfunction usePermissiveTarget(target, onTarget) {\n  watch(\n    () => unrefElement(target),\n    (el) => {\n      if (!el)\n        return;\n      onTarget(el);\n    },\n    {\n      immediate: true\n    }\n  );\n}\n\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\"\n};\nfunction reactiveTransform(props = {}, enableHardwareAcceleration = true) {\n  const state = reactive({ ...props });\n  const transform = ref(\"\");\n  watch(\n    state,\n    (newVal) => {\n      let result = \"\";\n      let hasHardwareAcceleration = false;\n      if (enableHardwareAcceleration && (newVal.x || newVal.y || newVal.z)) {\n        const str = [newVal.x || 0, newVal.y || 0, newVal.z || 0].map((val) => getValueAsType(val, px)).join(\",\");\n        result += `translate3d(${str}) `;\n        hasHardwareAcceleration = true;\n      }\n      for (const [key, value] of Object.entries(newVal)) {\n        if (enableHardwareAcceleration && (key === \"x\" || key === \"y\" || key === \"z\"))\n          continue;\n        const valueType = getValueType(key);\n        const valueAsType = getValueAsType(value, valueType);\n        result += `${translateAlias[key] || key}(${valueAsType}) `;\n      }\n      if (enableHardwareAcceleration && !hasHardwareAcceleration)\n        result += \"translateZ(0px) \";\n      transform.value = result.trim();\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  return {\n    state,\n    transform\n  };\n}\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst order = [\"perspective\", \"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformProps = [\"transformPerspective\", \"x\", \"y\", \"z\"];\norder.forEach((operationKey) => {\n  transformAxes.forEach((axesKey) => {\n    const key = operationKey + axesKey;\n    transformProps.push(key);\n  });\n});\nconst transformPropSet = new Set(transformProps);\nfunction isTransformProp(key) {\n  return transformPropSet.has(key);\n}\nconst transformOriginProps = /* @__PURE__ */ new Set([\"originX\", \"originY\", \"originZ\"]);\nfunction isTransformOriginProp(key) {\n  return transformOriginProps.has(key);\n}\nfunction splitValues(variant) {\n  const transform = {};\n  const style = {};\n  Object.entries(variant).forEach(([key, value]) => {\n    if (isTransformProp(key) || isTransformOriginProp(key))\n      transform[key] = value;\n    else style[key] = value;\n  });\n  return { transform, style };\n}\nfunction variantToStyle(variant) {\n  const { transform: _transform, style: _style } = splitValues(variant);\n  const { transform } = reactiveTransform(_transform);\n  const { style } = reactiveStyle(_style);\n  if (transform.value)\n    style.value.transform = transform.value;\n  return style.value;\n}\n\nfunction useElementStyle(target, onInit) {\n  let _cache;\n  let _target;\n  const { state, style } = reactiveStyle();\n  usePermissiveTarget(target, (el) => {\n    _target = el;\n    for (const key of Object.keys(valueTypes)) {\n      if (el.style[key] === null || el.style[key] === \"\" || isTransformProp(key) || isTransformOriginProp(key))\n        continue;\n      state[key] = el.style[key];\n    }\n    if (_cache) {\n      Object.entries(_cache).forEach(([key, value]) => el.style[key] = value);\n    }\n    if (onInit)\n      onInit(state);\n  });\n  watch(\n    style,\n    (newVal) => {\n      if (!_target) {\n        _cache = newVal;\n        return;\n      }\n      for (const key in newVal) _target.style[key] = newVal[key];\n    },\n    {\n      immediate: true\n    }\n  );\n  return {\n    style: state\n  };\n}\n\nfunction parseTransform(transform) {\n  const transforms = transform.trim().split(/\\) |\\)/);\n  if (transforms.length === 1)\n    return {};\n  const parseValues = (value) => {\n    if (value.endsWith(\"px\") || value.endsWith(\"deg\"))\n      return Number.parseFloat(value);\n    if (Number.isNaN(Number(value)))\n      return Number(value);\n    return value;\n  };\n  return transforms.reduce((acc, transform2) => {\n    if (!transform2)\n      return acc;\n    const [name, transformValue] = transform2.split(\"(\");\n    const valueArray = transformValue.split(\",\");\n    const values = valueArray.map((val) => {\n      return parseValues(val.endsWith(\")\") ? val.replace(\")\", \"\") : val.trim());\n    });\n    const value = values.length === 1 ? values[0] : values;\n    return {\n      ...acc,\n      [name]: value\n    };\n  }, {});\n}\nfunction stateFromTransform(state, transform) {\n  Object.entries(parseTransform(transform)).forEach(([key, value]) => {\n    const axes = [\"x\", \"y\", \"z\"];\n    if (key === \"translate3d\") {\n      if (value === 0) {\n        axes.forEach((axis) => state[axis] = 0);\n        return;\n      }\n      value.forEach((axisValue, index) => state[axes[index]] = axisValue);\n      return;\n    }\n    value = Number.parseFloat(`${value}`);\n    if (key === \"translateX\") {\n      state.x = value;\n      return;\n    }\n    if (key === \"translateY\") {\n      state.y = value;\n      return;\n    }\n    if (key === \"translateZ\") {\n      state.z = value;\n      return;\n    }\n    state[key] = value;\n  });\n}\n\nfunction useElementTransform(target, onInit) {\n  let _cache;\n  let _target;\n  const { state, transform } = reactiveTransform();\n  usePermissiveTarget(target, (el) => {\n    _target = el;\n    if (el.style.transform)\n      stateFromTransform(state, el.style.transform);\n    if (_cache)\n      el.style.transform = _cache;\n    if (onInit)\n      onInit(state);\n  });\n  watch(\n    transform,\n    (newValue) => {\n      if (!_target) {\n        _cache = newValue;\n        return;\n      }\n      _target.style.transform = newValue;\n    },\n    {\n      immediate: true\n    }\n  );\n  return {\n    transform: state\n  };\n}\n\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\n\nfunction useMotionProperties(target, defaultValues) {\n  const motionProperties = reactive({});\n  const apply = (values) => Object.entries(values).forEach(([key, value]) => motionProperties[key] = value);\n  const { style } = useElementStyle(target, apply);\n  const { transform } = useElementTransform(target, apply);\n  watch(\n    motionProperties,\n    (newVal) => {\n      objectEntries(newVal).forEach(([key, value]) => {\n        const target2 = isTransformProp(key) ? transform : style;\n        if (target2[key] && target2[key] === value)\n          return;\n        target2[key] = value;\n      });\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  usePermissiveTarget(target, () => defaultValues && apply(defaultValues));\n  return {\n    motionProperties,\n    style,\n    transform\n  };\n}\n\nfunction useMotionVariants(variants = {}) {\n  const _variants = unref(variants);\n  const variant = ref();\n  const state = computed(() => {\n    if (!variant.value)\n      return;\n    return _variants[variant.value];\n  });\n  return {\n    state,\n    variant\n  };\n}\n\nfunction useMotion(target, variants = {}, options) {\n  const { motionProperties } = useMotionProperties(target);\n  const { variant, state } = useMotionVariants(variants);\n  const controls = useMotionControls(motionProperties, variants);\n  const instance = {\n    target,\n    variant,\n    variants,\n    state,\n    motionProperties,\n    ...controls\n  };\n  useMotionFeatures(instance, options);\n  return instance;\n}\n\nconst transitionKeys = [\"delay\", \"duration\"];\nconst directivePropsKeys = [\"initial\", \"enter\", \"leave\", \"visible\", \"visible-once\", \"visibleOnce\", \"hovered\", \"tapped\", \"focused\", ...transitionKeys];\nfunction isTransitionKey(val) {\n  return transitionKeys.includes(val);\n}\nfunction resolveVariants(node, variantsRef) {\n  const target = node.props ? node.props : node.data && node.data.attrs ? node.data.attrs : {};\n  if (target) {\n    if (target.variants && isObject$1(target.variants)) {\n      variantsRef.value = {\n        ...variantsRef.value,\n        ...target.variants\n      };\n    }\n    for (let key of directivePropsKeys) {\n      if (!target || !target[key])\n        continue;\n      if (isTransitionKey(key) && typeof target[key] === \"number\") {\n        for (const variantKey of [\"enter\", \"visible\", \"visibleOnce\"]) {\n          const variantConfig = variantsRef.value[variantKey];\n          if (variantConfig == null)\n            continue;\n          variantConfig.transition ??= {};\n          variantConfig.transition[key] = target[key];\n        }\n        continue;\n      }\n      if (isObject$1(target[key])) {\n        const prop = target[key];\n        if (key === \"visible-once\")\n          key = \"visibleOnce\";\n        variantsRef.value[key] = prop;\n      }\n    }\n  }\n}\n\nfunction directive(variants, isPreset = false) {\n  const register = (el, binding, node) => {\n    const key = binding.value && typeof binding.value === \"string\" ? binding.value : node.key;\n    if (key && motionState[key])\n      motionState[key].stop();\n    const variantsObject = isPreset ? structuredClone(toRaw(variants) || {}) : variants || {};\n    const variantsRef = ref(variantsObject);\n    if (typeof binding.value === \"object\")\n      variantsRef.value = binding.value;\n    resolveVariants(node, variantsRef);\n    const motionOptions = { eventListeners: true, lifeCycleHooks: true, syncVariants: true, visibilityHooks: false };\n    const motionInstance = useMotion(\n      el,\n      variantsRef,\n      motionOptions\n    );\n    el.motionInstance = motionInstance;\n    if (key)\n      motionState[key] = motionInstance;\n  };\n  const mounted = (el, _binding, _node) => {\n    el.motionInstance && registerVisibilityHooks(el.motionInstance);\n  };\n  return {\n    created: register,\n    mounted,\n    getSSRProps(binding, node) {\n      let { initial: bindingInitial } = binding.value || node && node?.props || {};\n      bindingInitial = unref(bindingInitial);\n      const initial = defu({}, variants?.initial || {}, bindingInitial || {});\n      if (!initial || Object.keys(initial).length === 0)\n        return;\n      const style = variantToStyle(initial);\n      return {\n        style\n      };\n    }\n  };\n}\n\nconst fade = {\n  initial: {\n    opacity: 0\n  },\n  enter: {\n    opacity: 1\n  }\n};\nconst fadeVisible = {\n  initial: {\n    opacity: 0\n  },\n  visible: {\n    opacity: 1\n  }\n};\nconst fadeVisibleOnce = {\n  initial: {\n    opacity: 0\n  },\n  visibleOnce: {\n    opacity: 1\n  }\n};\n\nconst pop = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  enter: {\n    scale: 1,\n    opacity: 1\n  }\n};\nconst popVisible = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  visible: {\n    scale: 1,\n    opacity: 1\n  }\n};\nconst popVisibleOnce = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  visibleOnce: {\n    scale: 1,\n    opacity: 1\n  }\n};\n\nconst rollLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\n\nconst slideLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    opacity: 1\n  }\n};\n\nconst presets = {\n  __proto__: null,\n  fade: fade,\n  fadeVisible: fadeVisible,\n  fadeVisibleOnce: fadeVisibleOnce,\n  pop: pop,\n  popVisible: popVisible,\n  popVisibleOnce: popVisibleOnce,\n  rollBottom: rollBottom,\n  rollLeft: rollLeft,\n  rollRight: rollRight,\n  rollTop: rollTop,\n  rollVisibleBottom: rollVisibleBottom,\n  rollVisibleLeft: rollVisibleLeft,\n  rollVisibleOnceBottom: rollVisibleOnceBottom,\n  rollVisibleOnceLeft: rollVisibleOnceLeft,\n  rollVisibleOnceRight: rollVisibleOnceRight,\n  rollVisibleOnceTop: rollVisibleOnceTop,\n  rollVisibleRight: rollVisibleRight,\n  rollVisibleTop: rollVisibleTop,\n  slideBottom: slideBottom,\n  slideLeft: slideLeft,\n  slideRight: slideRight,\n  slideTop: slideTop,\n  slideVisibleBottom: slideVisibleBottom,\n  slideVisibleLeft: slideVisibleLeft,\n  slideVisibleOnceBottom: slideVisibleOnceBottom,\n  slideVisibleOnceLeft: slideVisibleOnceLeft,\n  slideVisibleOnceRight: slideVisibleOnceRight,\n  slideVisibleOnceTop: slideVisibleOnceTop,\n  slideVisibleRight: slideVisibleRight,\n  slideVisibleTop: slideVisibleTop\n};\n\nfunction slugify(str) {\n  const a = \"\\xE0\\xE1\\xE2\\xE4\\xE6\\xE3\\xE5\\u0101\\u0103\\u0105\\xE7\\u0107\\u010D\\u0111\\u010F\\xE8\\xE9\\xEA\\xEB\\u0113\\u0117\\u0119\\u011B\\u011F\\u01F5\\u1E27\\xEE\\xEF\\xED\\u012B\\u012F\\xEC\\u0142\\u1E3F\\xF1\\u0144\\u01F9\\u0148\\xF4\\xF6\\xF2\\xF3\\u0153\\xF8\\u014D\\xF5\\u0151\\u1E55\\u0155\\u0159\\xDF\\u015B\\u0161\\u015F\\u0219\\u0165\\u021B\\xFB\\xFC\\xF9\\xFA\\u016B\\u01D8\\u016F\\u0171\\u0173\\u1E83\\u1E8D\\xFF\\xFD\\u017E\\u017A\\u017C\\xB7/_,:;\";\n  const b = \"aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrsssssttuuuuuuuuuwxyyzzz------\";\n  const p = new RegExp(a.split(\"\").join(\"|\"), \"g\");\n  return str.toString().replace(/[A-Z]/g, (s) => `-${s}`).toLowerCase().replace(/\\s+/g, \"-\").replace(p, (c) => b.charAt(a.indexOf(c))).replace(/&/g, \"-and-\").replace(/[^\\w\\-]+/g, \"\").replace(/-{2,}/g, \"-\").replace(/^-+/, \"\").replace(/-+$/, \"\");\n}\n\nconst CUSTOM_PRESETS = Symbol(\n  import.meta.env?.MODE === \"development\" ? \"motionCustomPresets\" : \"\"\n);\n\nconst MotionComponentProps = {\n  // Preset to be loaded\n  preset: {\n    type: String,\n    required: false\n  },\n  // Instance\n  instance: {\n    type: Object,\n    required: false\n  },\n  // Variants\n  variants: {\n    type: Object,\n    required: false\n  },\n  // Initial variant\n  initial: {\n    type: Object,\n    required: false\n  },\n  // Lifecycle hooks variants\n  enter: {\n    type: Object,\n    required: false\n  },\n  leave: {\n    type: Object,\n    required: false\n  },\n  // Intersection observer variants\n  visible: {\n    type: Object,\n    required: false\n  },\n  visibleOnce: {\n    type: Object,\n    required: false\n  },\n  // Event listeners variants\n  hovered: {\n    type: Object,\n    required: false\n  },\n  tapped: {\n    type: Object,\n    required: false\n  },\n  focused: {\n    type: Object,\n    required: false\n  },\n  // Helpers\n  delay: {\n    type: [Number, String],\n    required: false\n  },\n  duration: {\n    type: [Number, String],\n    required: false\n  }\n};\nfunction isObject(val) {\n  return Object.prototype.toString.call(val) === \"[object Object]\";\n}\nfunction clone(v) {\n  if (Array.isArray(v)) {\n    return v.map(clone);\n  }\n  if (isObject(v)) {\n    const res = {};\n    for (const key in v) {\n      res[key] = clone(v[key]);\n    }\n    return res;\n  }\n  return v;\n}\nfunction setupMotionComponent(props) {\n  const instances = reactive({});\n  const customPresets = inject(CUSTOM_PRESETS, {});\n  const preset = computed(() => {\n    if (props.preset == null) {\n      return {};\n    }\n    if (customPresets != null && props.preset in customPresets) {\n      return structuredClone(toRaw(customPresets)[props.preset]);\n    }\n    if (props.preset in presets) {\n      return structuredClone(presets[props.preset]);\n    }\n    return {};\n  });\n  const propsConfig = computed(() => ({\n    initial: props.initial,\n    enter: props.enter,\n    leave: props.leave,\n    visible: props.visible,\n    visibleOnce: props.visibleOnce,\n    hovered: props.hovered,\n    tapped: props.tapped,\n    focused: props.focused\n  }));\n  function applyTransitionHelpers(config, values) {\n    for (const transitionKey of [\"delay\", \"duration\"]) {\n      if (values[transitionKey] == null)\n        continue;\n      const transitionValueParsed = Number.parseInt(\n        values[transitionKey]\n      );\n      for (const variantKey of [\"enter\", \"visible\", \"visibleOnce\"]) {\n        const variantConfig = config[variantKey];\n        if (variantConfig == null)\n          continue;\n        variantConfig.transition ??= {};\n        variantConfig.transition[transitionKey] = transitionValueParsed;\n      }\n    }\n    return config;\n  }\n  const motionConfig = computed(() => {\n    const config = defu(\n      {},\n      propsConfig.value,\n      preset.value,\n      props.variants || {}\n    );\n    return applyTransitionHelpers({ ...config }, props);\n  });\n  if (import.meta.env?.MODE === \"development\") {\n    if (props.preset != null && presets?.[props.preset] == null && customPresets?.[props.preset] == null) {\n      console.warn(`[@vueuse/motion]: Preset \\`${props.preset}\\` not found.`);\n    }\n    const replayAnimation = (instance) => {\n      if (instance.variants?.initial) {\n        instance.set(\"initial\");\n      }\n      nextTick(() => {\n        if (instance.variants?.enter)\n          instance.apply(\"enter\");\n        if (instance.variants?.visible)\n          instance.apply(\"visible\");\n        if (instance.variants?.visibleOnce)\n          instance.apply(\"visibleOnce\");\n      });\n    };\n    onUpdated(() => {\n      for (const key in instances) {\n        replayAnimation(instances[key]);\n      }\n    });\n  }\n  function setNodeInstance(node, index, style) {\n    node.props ??= {};\n    node.props.style ??= {};\n    node.props.style = { ...node.props.style, ...style };\n    const elementMotionConfig = applyTransitionHelpers(\n      clone(motionConfig.value),\n      node.props\n    );\n    node.props.onVnodeMounted = ({ el }) => {\n      instances[index] = useMotion(\n        el,\n        elementMotionConfig\n      );\n    };\n    node.props.onVnodeUpdated = ({ el }) => {\n      const styles = variantToStyle(instances[index].state);\n      for (const [key, val] of Object.entries(styles)) {\n        el.style[key] = val;\n      }\n    };\n    return node;\n  }\n  return {\n    motionConfig,\n    setNodeInstance\n  };\n}\n\nconst MotionComponent = defineComponent({\n  name: \"Motion\",\n  props: {\n    ...MotionComponentProps,\n    is: {\n      type: [String, Object],\n      default: \"div\"\n    }\n  },\n  setup(props) {\n    const slots = useSlots();\n    const { motionConfig, setNodeInstance } = setupMotionComponent(props);\n    return () => {\n      const style = variantToStyle(motionConfig.value.initial || {});\n      const node = h(props.is, void 0, slots);\n      setNodeInstance(node, 0, style);\n      return node;\n    };\n  }\n});\n\nconst MotionGroupComponent = defineComponent({\n  name: \"MotionGroup\",\n  props: {\n    ...MotionComponentProps,\n    is: {\n      type: [String, Object],\n      required: false\n    }\n  },\n  setup(props) {\n    const slots = useSlots();\n    const { motionConfig, setNodeInstance } = setupMotionComponent(props);\n    return () => {\n      const style = variantToStyle(motionConfig.value.initial || {});\n      const nodes = slots.default?.() || [];\n      for (let i = 0; i < nodes.length; i++) {\n        const n = nodes[i];\n        if (n.type === Fragment && Array.isArray(n.children)) {\n          n.children.forEach(function setChildInstance(child, index) {\n            if (child == null)\n              return;\n            if (Array.isArray(child)) {\n              setChildInstance(child, index);\n              return;\n            }\n            if (typeof child === \"object\") {\n              setNodeInstance(child, index, style);\n            }\n          });\n        } else {\n          setNodeInstance(n, i, style);\n        }\n      }\n      if (props.is) {\n        return h(props.is, void 0, nodes);\n      }\n      return nodes;\n    };\n  }\n});\n\nconst MotionPlugin = {\n  install(app, options) {\n    app.directive(\"motion\", directive());\n    if (!options || options && !options.excludePresets) {\n      for (const key in presets) {\n        const preset = presets[key];\n        app.directive(`motion-${slugify(key)}`, directive(preset, true));\n      }\n    }\n    if (options && options.directives) {\n      for (const key in options.directives) {\n        const variants = options.directives[key];\n        if (!variants.initial && import.meta.env?.MODE === \"development\") {\n          console.warn(\n            `Your directive v-motion-${key} is missing initial variant!`\n          );\n        }\n        app.directive(`motion-${key}`, directive(variants, true));\n      }\n    }\n    app.provide(CUSTOM_PRESETS, options?.directives);\n    app.component(\"Motion\", MotionComponent);\n    app.component(\"MotionGroup\", MotionGroupComponent);\n  }\n};\n\nfunction isMotionInstance(obj) {\n  const _obj = obj;\n  return _obj.apply !== void 0 && typeof _obj.apply === \"function\" && _obj.set !== void 0 && typeof _obj.set === \"function\" && _obj.target !== void 0 && isRef(_obj.target);\n}\n\nfunction useMotions() {\n  return motionState;\n}\n\nfunction useSpring(values, spring) {\n  const { stop, get } = useMotionValues();\n  return {\n    values,\n    stop,\n    set: (properties) => Promise.all(\n      Object.entries(properties).map(([key, value]) => {\n        const motionValue = get(key, values[key], values);\n        return motionValue.start((onComplete) => {\n          const options = {\n            type: \"spring\",\n            ...spring || getDefaultTransition(key, value)\n          };\n          return animate({\n            from: motionValue.get(),\n            to: value,\n            velocity: motionValue.getVelocity(),\n            onUpdate: (v) => motionValue.set(v),\n            onComplete,\n            ...options\n          });\n        });\n      })\n    )\n  };\n}\n\nfunction useReducedMotion(options = {}) {\n  const reducedMotion = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => reducedMotion.value);\n}\n\nexport { MotionComponent, directive as MotionDirective, MotionGroupComponent, MotionPlugin, fade, fadeVisible, fadeVisibleOnce, isMotionInstance, pop, popVisible, popVisibleOnce, reactiveStyle, reactiveTransform, rollBottom, rollLeft, rollRight, rollTop, rollVisibleBottom, rollVisibleLeft, rollVisibleOnceBottom, rollVisibleOnceLeft, rollVisibleOnceRight, rollVisibleOnceTop, rollVisibleRight, rollVisibleTop, slideBottom, slideLeft, slideRight, slideTop, slideVisibleBottom, slideVisibleLeft, slideVisibleOnceBottom, slideVisibleOnceLeft, slideVisibleOnceRight, slideVisibleOnceTop, slideVisibleRight, slideVisibleTop, slugify, useElementStyle, useElementTransform, useMotion, useMotionControls, useMotionFeatures, useMotionProperties, useMotionTransitions, useMotionVariants, useMotions, useReducedMotion, useSpring };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,cAAc,OAAO;AAC5B,MAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C,WAAO;AAAA,EACT;AACA,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,MAAI,cAAc,QAAQ,cAAc,OAAO,aAAa,OAAO,eAAe,SAAS,MAAM,MAAM;AACrG,WAAO;AAAA,EACT;AACA,MAAI,OAAO,YAAY,OAAO;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,eAAe,OAAO;AAC/B,WAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAAA,EACnD;AACA,SAAO;AACT;AAEA,SAAS,MAAM,YAAY,UAAU,YAAY,KAAK,QAAQ;AAC5D,MAAI,CAAC,cAAc,QAAQ,GAAG;AAC5B,WAAO,MAAM,YAAY,CAAC,GAAG,WAAW,MAAM;AAAA,EAChD;AACA,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ;AACzC,aAAW,OAAO,YAAY;AAC5B,QAAI,QAAQ,eAAe,QAAQ,eAAe;AAChD;AAAA,IACF;AACA,UAAM,QAAQ,WAAW,GAAG;AAC5B,QAAI,UAAU,QAAQ,UAAU,QAAQ;AACtC;AAAA,IACF;AACA,QAAI,UAAU,OAAO,QAAQ,KAAK,OAAO,SAAS,GAAG;AACnD;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AACtD,aAAO,GAAG,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC;AAAA,IACzC,WAAW,cAAc,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC7D,aAAO,GAAG,IAAI;AAAA,QACZ;AAAA,QACA,OAAO,GAAG;AAAA,SACT,YAAY,GAAG,SAAS,MAAM,MAAM,IAAI,SAAS;AAAA,QAClD;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,IAAI;AAAA;AAAA,IAET,WAAW,OAAO,CAAC,GAAGA,OAAM,MAAM,GAAGA,IAAG,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA;AAE3D;AACA,IAAM,OAAO,WAAW;AACxB,IAAM,SAAS,WAAW,CAAC,QAAQ,KAAK,iBAAiB;AACvD,MAAI,OAAO,GAAG,MAAM,UAAU,OAAO,iBAAiB,YAAY;AAChE,WAAO,GAAG,IAAI,aAAa,OAAO,GAAG,CAAC;AACtC,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAM,cAAc,WAAW,CAAC,QAAQ,KAAK,iBAAiB;AAC5D,MAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,KAAK,OAAO,iBAAiB,YAAY;AACpE,WAAO,GAAG,IAAI,aAAa,OAAO,GAAG,CAAC;AACtC,WAAO;AAAA,EACT;AACF,CAAC;;;ACnBD,SAAS,kBAAkB,IAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAe,EAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AA4CA,IAAM,wBAAwC,oBAAI,QAAQ;AAE1D,IAAM,cAAyC,IAAI,SAAS;AAC1D,MAAIC;AACJ,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,YAAYA,MAAK,mBAAmB,MAAM,OAAO,SAASA,IAAG;AACnE,MAAI,YAAY,QAAQ,CAAC,oBAAoB;AAC3C,UAAM,IAAI,MAAM,qCAAqC;AACvD,MAAI,YAAY,sBAAsB,IAAI,QAAQ,KAAK,OAAO,sBAAsB,IAAI,QAAQ;AAC9F,WAAO,sBAAsB,IAAI,QAAQ,EAAE,GAAG;AAChD,SAAO,OAAO,GAAG,IAAI;AACvB;AA4LA,IAAM,WAAW,OAAO,WAAW,eAAe,OAAO,aAAa;AACtE,IAAM,WAAW,OAAO,sBAAsB,eAAe,sBAAsB;AAEnF,IAAM,aAAa,CAAC,QAAQ,OAAO;AAKnC,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,WAAW,CAAC,QAAQ,SAAS,KAAK,GAAG,MAAM;AAIjD,IAAM,OAAO,MAAM;AACnB;AAOA,IAAM,QAAwB,SAAS;AACvC,SAAS,WAAW;AAClB,MAAIC,KAAI;AACR,SAAO,cAAcA,MAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAASA,IAAG,eAAe,mBAAmB,KAAK,OAAO,UAAU,SAAS,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,KAAK,UAAU,OAAO,SAAS,OAAO,UAAU,SAAS;AAC9U;AAgLA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AAiCA,SAAS,QAAQC,KAAI;AACnB,SAAOA,IAAG,SAAS,KAAK,IAAI,OAAO,WAAWA,GAAE,IAAI,KAAK,OAAO,WAAWA,GAAE;AAC/E;AAkBA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AAEA,SAAS,oBAAoB,IAAI;AAC/B,QAAM,QAAwB,uBAAO,OAAO,IAAI;AAChD,SAAO,CAAC,QAAQ;AACd,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AACA,IAAM,cAAc;AACpB,IAAM,YAAY,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,aAAa,KAAK,EAAE,YAAY,CAAC;AAC5F,IAAM,aAAa;AACnB,IAAM,WAAW,oBAAoB,CAAC,QAAQ;AAC5C,SAAO,IAAI,QAAQ,YAAY,CAAC,GAAGC,OAAMA,KAAIA,GAAE,YAAY,IAAI,EAAE;AACnE,CAAC;AAED,SAAS,mBAAmB,QAAQ;AAClC,SAAO,UAAU,mBAAmB;AACtC;AAqQA,SAAS,eAAe,IAAI,QAAQ;AAClC,QAAM,WAAW,mBAAmB,MAAM;AAC1C,MAAI;AACF,gBAAY,IAAI,MAAM;AAC1B;AAisBA,SAAS,eAAe,QAAQC,KAAI,SAAS;AAC3C,SAAO;AAAA,IACL;AAAA,IACAA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,WAAW;AAAA,IACb;AAAA,EACF;AACF;;;AC91CA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AAErD,SAAS,aAAa,OAAO;AAC3B,MAAIC;AACJ,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQA,MAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAOA,MAAK;AAClE;AAEA,SAAS,oBAAoB,MAAM;AACjC,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,YAAY;AACjD,OAAG,iBAAiB,OAAO,UAAU,OAAO;AAC5C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,OAAO;AAAA,EAC9D;AACA,QAAM,oBAAoB,SAAS,MAAM;AACvC,UAAMC,QAAO,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAC9D,WAAOA,MAAK,MAAM,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAIA,QAAO;AAAA,EAC3D,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM;AACJ,UAAID,KAAI;AACR,aAAO;AAAA,SACJ,MAAMA,MAAK,kBAAkB,UAAU,OAAO,SAASA,IAAG,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAAA,QAC9I,QAAQ,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,QAC5D,QAAQ,MAAM,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA;AAAA,QAE1D,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,IACA,CAAC,CAAC,aAAa,YAAY,eAAe,WAAW,MAAM;AACzD,cAAQ;AACR,UAAI,EAAE,eAAe,OAAO,SAAS,YAAY,WAAW,EAAE,cAAc,OAAO,SAAS,WAAW,WAAW,EAAE,iBAAiB,OAAO,SAAS,cAAc;AACjK;AACF,YAAM,eAAe,SAAS,WAAW,IAAI,EAAE,GAAG,YAAY,IAAI;AAClE,eAAS;AAAA,QACP,GAAG,YAAY;AAAA,UACb,CAAC,OAAO,WAAW;AAAA,YACjB,CAAC,UAAU,cAAc,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,YAAY,CAAC;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,OAAO;AACzB,SAAO;AACT;AA+FA,SAAS,aAAa;AACpB,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,WAAW,mBAAmB;AACpC,MAAI,UAAU;AACZ,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,GAAG,QAAQ;AAAA,EACb;AACA,SAAO;AACT;AAGA,SAAS,aAAa,UAAU;AAC9B,QAAM,YAAY,WAAW;AAC7B,SAAO,SAAS,MAAM;AACpB,cAAU;AACV,WAAO,QAAQ,SAAS,CAAC;AAAA,EAC3B,CAAC;AACH;AAk7BA,IAAM,iBAAiB,OAAO,kBAAkB;AAEhD,SAAS,cAAc;AACrB,QAAM,WAAW,oBAAoB,IAAI,YAAY,gBAAgB,IAAI,IAAI;AAC7E,SAAO,OAAO,aAAa,WAAW,WAAW;AACnD;AASA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAE,UAAS,eAAe,WAAW,YAAY,EAAE,IAAI;AAC7D,QAAM,cAAc,aAAa,MAAMA,WAAU,gBAAgBA,WAAU,OAAOA,QAAO,eAAe,UAAU;AAClH,QAAM,aAAa,WAAW,OAAO,aAAa,QAAQ;AAC1D,QAAM,aAAa,WAAW;AAC9B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,UAAU,CAAC,UAAU;AACzB,YAAQ,QAAQ,MAAM;AAAA,EACxB;AACA,cAAY,MAAM;AAChB,QAAI,WAAW,OAAO;AACpB,iBAAW,QAAQ,CAAC,YAAY;AAChC,YAAM,eAAe,QAAQ,KAAK,EAAE,MAAM,GAAG;AAC7C,cAAQ,QAAQ,aAAa,KAAK,CAAC,gBAAgB;AACjD,cAAM,MAAM,YAAY,SAAS,SAAS;AAC1C,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,YAAI,MAAM,QAAQ,YAAY,QAAQ;AACtC,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,eAAO,MAAM,CAAC,MAAM;AAAA,MACtB,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQA,QAAO,WAAW,QAAQ,KAAK,CAAC;AACnD,YAAQ,QAAQ,WAAW,MAAM;AAAA,EACnC,CAAC;AACD,mBAAiB,YAAY,UAAU,SAAS,EAAE,SAAS,KAAK,CAAC;AACjE,SAAO,SAAS,MAAM,QAAQ,KAAK;AACrC;AA4cA,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,IAAM,WAA2B,YAAY;AAC7C,SAAS,cAAc;AACrB,MAAI,EAAE,aAAa;AACjB,YAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC9C,SAAO,QAAQ,SAAS;AAC1B;AAg0CA,SAAS,wBAAwB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAAC,UAAS;AAAA,IACT,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,0BAA0BA,OAAM;AACjF,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,UAAU,QAAQ,MAAM;AAC9B,WAAO,QAAQ,OAAO,EAAE,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,EAC7D,CAAC;AACD,MAAI,UAAU;AACd,QAAM,WAAW,WAAW,SAAS;AACrC,QAAM,YAAY,YAAY,QAAQ;AAAA,IACpC,MAAM,CAAC,QAAQ,OAAO,aAAa,IAAI,GAAG,SAAS,KAAK;AAAA,IACxD,CAAC,CAAC,UAAU,KAAK,MAAM;AACrB,cAAQ;AACR,UAAI,CAAC,SAAS;AACZ;AACF,UAAI,CAAC,SAAS;AACZ;AACF,YAAM,WAAW,IAAI;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM,aAAa,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,eAAS,QAAQ,CAAC,OAAO,MAAM,SAAS,QAAQ,EAAE,CAAC;AACnD,gBAAU,MAAM;AACd,iBAAS,WAAW;AACpB,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,EAAE,WAAW,OAAO,OAAO;AAAA,EAC7B,IAAI;AACJ,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AACV,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ;AACN,cAAQ;AACR,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA,SAAS;AACP,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACF;AA4+EA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AA8mCrD,IAAM,gBAAgB;AAAA,EACpB,EAAE,KAAK,KAAK,OAAO,KAAK,MAAM,SAAS;AAAA,EACvC,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,SAAS;AAAA,EACzC,EAAE,KAAK,MAAM,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC,EAAE,KAAK,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,EACzC,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AAAA,EAC5C,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAAA,EAC7C,EAAE,KAAK,OAAO,mBAAmB,OAAO,SAAS,MAAM,OAAO;AAChE;AA4PA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoC,OAAO,OAAO,CAAC,GAAG,EAAE,QAAQ,SAAS,GAAG,kBAAkB;;;AC9gOpG,IAAM,kBAAmB,IAAI,KAAM;AACnC,IAAM,iBAAiB,OAAO,gBAAgB,cACxC,MAAM,YAAY,IAAI,IACtB,MAAM,KAAK,IAAI;AACrB,IAAM,cAAc,OAAO,WAAW,cAChC,CAAC,aAAa,OAAO,sBAAsB,QAAQ,IACnD,CAAC,aAAa,WAAW,MAAM,SAAS,eAAe,CAAC,GAAG,eAAe;;;ACNhF,SAAS,iBAAiBC,eAAc;AACpC,MAAI,QAAQ,CAAC;AACb,MAAI,iBAAiB,CAAC;AACtB,MAAI,WAAW;AACf,MAAIC,gBAAe;AACnB,MAAI,iBAAiB;AACrB,QAAM,cAAc,oBAAI,QAAQ;AAChC,QAAM,OAAO;AAAA,IACT,UAAU,CAAC,UAAU,YAAY,OAAO,YAAY,UAAU;AAC1D,YAAM,oBAAoB,aAAaA;AACvC,YAAM,SAAS,oBAAoB,QAAQ;AAC3C,UAAI;AACA,oBAAY,IAAI,QAAQ;AAC5B,UAAI,OAAO,QAAQ,QAAQ,MAAM,IAAI;AACjC,eAAO,KAAK,QAAQ;AACpB,YAAI,qBAAqBA;AACrB,qBAAW,MAAM;AAAA,MACzB;AACA,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,CAAC,aAAa;AAClB,YAAM,QAAQ,eAAe,QAAQ,QAAQ;AAC7C,UAAI,UAAU;AACV,uBAAe,OAAO,OAAO,CAAC;AAClC,kBAAY,OAAO,QAAQ;AAAA,IAC/B;AAAA,IACA,SAAS,CAAC,cAAc;AACpB,UAAIA,eAAc;AACd,yBAAiB;AACjB;AAAA,MACJ;AACA,MAAAA,gBAAe;AACf,OAAC,OAAO,cAAc,IAAI,CAAC,gBAAgB,KAAK;AAChD,qBAAe,SAAS;AACxB,iBAAW,MAAM;AACjB,UAAI,UAAU;AACV,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,gBAAM,WAAW,MAAM,CAAC;AACxB,mBAAS,SAAS;AAClB,cAAI,YAAY,IAAI,QAAQ,GAAG;AAC3B,iBAAK,SAAS,QAAQ;AACtB,YAAAD,cAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACA,MAAAC,gBAAe;AACf,UAAI,gBAAgB;AAChB,yBAAiB;AACjB,aAAK,QAAQ,SAAS;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AClDA,IAAM,aAAa;AACnB,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAM,QAAQ;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AACf;AACA,IAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,QAAQ,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC1C,MAAI,GAAG,IAAI,iBAAiB,MAAO,eAAe,IAAK;AACvD,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,OAAO,WAAW,OAAO,CAAC,KAAK,QAAQ;AACzC,QAAM,OAAO,MAAM,GAAG;AACtB,MAAI,GAAG,IAAI,CAACC,UAAS,YAAY,OAAO,YAAY,UAAU;AAC1D,QAAI,CAAC;AACD,gBAAU;AACd,WAAO,KAAK,SAASA,UAAS,WAAW,SAAS;AAAA,EACtD;AACA,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,aAAa,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC/C,MAAI,GAAG,IAAI,MAAM,GAAG,EAAE;AACtB,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,YAAY,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC9C,MAAI,GAAG,IAAI,MAAM,MAAM,GAAG,EAAE,QAAQ,KAAK;AACzC,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,cAAc,CAAC,WAAW,MAAM,MAAM,EAAE,QAAQ,KAAK;AAC3D,IAAM,eAAe,CAACC,eAAc;AAChC,iBAAe;AACf,QAAM,QAAQ,oBACR,kBACA,KAAK,IAAI,KAAK,IAAIA,aAAY,MAAM,WAAW,UAAU,GAAG,CAAC;AACnE,QAAM,YAAYA;AAClB,iBAAe;AACf,aAAW,QAAQ,WAAW;AAC9B,iBAAe;AACf,MAAI,cAAc;AACd,wBAAoB;AACpB,gBAAY,YAAY;AAAA,EAC5B;AACJ;AACA,IAAM,YAAY,MAAM;AACpB,iBAAe;AACf,sBAAoB;AACpB,MAAI,CAAC;AACD,gBAAY,YAAY;AAChC;AACA,IAAM,eAAe,MAAM;AAE3B,IAAO,aAAQ;;;ACpBR,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;;;ACpDA,IAAI,UAAU,WAAY;AAAE;AAC5B,IAAI,YAAY,WAAY;AAAE;AAC9B,IAAI,MAAuC;AACvC,YAAU,SAAU,OAAO,SAAS;AAChC,QAAI,CAAC,SAAS,OAAO,YAAY,aAAa;AAC1C,cAAQ,KAAK,OAAO;AAAA,IACxB;AAAA,EACJ;AACA,cAAY,SAAU,OAAO,SAAS;AAClC,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,MAAM,OAAO;AAAA,IAC3B;AAAA,EACJ;AACJ;;;ACbA,IAAMC,SAAQ,CAAC,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;;;ACG7D,IAAM,UAAU;AAChB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,SAAS,WAAW,EAAE,WAAW,KAAK,SAAS,MAAM,WAAW,GAAG,OAAO,EAAG,GAAG;AAC5E,MAAI;AACJ,MAAI;AACJ,UAAQ,YAAY,cAAc,KAAM,4CAA4C;AACpF,MAAI,eAAe,IAAI;AACvB,iBAAeC,OAAM,YAAY,YAAY,YAAY;AACzD,aAAWA,OAAM,aAAa,aAAa,WAAW,GAAI;AAC1D,MAAI,eAAe,GAAG;AAClB,eAAW,CAACC,kBAAiB;AACzB,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAMC,KAAI,mBAAmB;AAC7B,YAAMC,KAAI,gBAAgBF,eAAc,YAAY;AACpD,YAAMG,KAAI,KAAK,IAAI,CAAC,KAAK;AACzB,aAAO,UAAWF,KAAIC,KAAKC;AAAA,IAC/B;AACA,iBAAa,CAACH,kBAAiB;AAC3B,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAM,IAAI,QAAQ,WAAW;AAC7B,YAAM,IAAI,KAAK,IAAI,cAAc,CAAC,IAAI,KAAK,IAAIA,eAAc,CAAC,IAAI;AAClE,YAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,YAAM,IAAI,gBAAgB,KAAK,IAAIA,eAAc,CAAC,GAAG,YAAY;AACjE,YAAM,SAAS,CAAC,SAASA,aAAY,IAAI,UAAU,IAAI,KAAK;AAC5D,aAAQ,WAAW,IAAI,KAAK,KAAM;AAAA,IACtC;AAAA,EACJ,OACK;AACD,eAAW,CAACA,kBAAiB;AACzB,YAAMC,KAAI,KAAK,IAAI,CAACD,gBAAe,QAAQ;AAC3C,YAAME,MAAKF,gBAAe,YAAY,WAAW;AACjD,aAAO,CAAC,UAAUC,KAAIC;AAAA,IAC1B;AACA,iBAAa,CAACF,kBAAiB;AAC3B,YAAMC,KAAI,KAAK,IAAI,CAACD,gBAAe,QAAQ;AAC3C,YAAME,MAAK,WAAWF,kBAAiB,WAAW;AAClD,aAAOC,KAAIC;AAAA,IACf;AAAA,EACJ;AACA,QAAM,eAAe,IAAI;AACzB,QAAM,eAAe,gBAAgB,UAAU,YAAY,YAAY;AACvE,aAAW,WAAW;AACtB,MAAI,MAAM,YAAY,GAAG;AACrB,WAAO;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,MACT;AAAA,IACJ;AAAA,EACJ,OACK;AACD,UAAM,YAAY,KAAK,IAAI,cAAc,CAAC,IAAI;AAC9C,WAAO;AAAA,MACH;AAAA,MACA,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,iBAAiB;AACvB,SAAS,gBAAgB,UAAU,YAAY,cAAc;AACzD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,aAAS,SAAS,SAAS,MAAM,IAAI,WAAW,MAAM;AAAA,EAC1D;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,cAAc,cAAc;AACjD,SAAO,eAAe,KAAK,KAAK,IAAI,eAAe,YAAY;AACnE;;;ACzEA,IAAM,eAAe,CAAC,YAAY,QAAQ;AAC1C,IAAM,cAAc,CAAC,aAAa,WAAW,MAAM;AACnD,SAAS,aAAa,SAASE,OAAM;AACjC,SAAOA,MAAK,KAAK,CAAC,QAAQ,QAAQ,GAAG,MAAM,MAAS;AACxD;AACA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,gBAAgB,OAAO,OAAO,EAAE,UAAU,GAAK,WAAW,KAAK,SAAS,IAAI,MAAM,GAAK,wBAAwB,MAAM,GAAG,OAAO;AACnI,MAAI,CAAC,aAAa,SAAS,WAAW,KAClC,aAAa,SAAS,YAAY,GAAG;AACrC,UAAM,UAAU,WAAW,OAAO;AAClC,oBAAgB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,OAAO,GAAG,EAAE,UAAU,GAAK,MAAM,EAAI,CAAC;AACpH,kBAAc,yBAAyB;AAAA,EAC3C;AACA,SAAO;AACX;AACA,SAAS,OAAOC,KAAI;AAChB,MAAI,EAAE,OAAO,GAAK,KAAK,GAAK,YAAY,GAAG,UAAU,IAAIA,KAAI,UAAU,OAAOA,KAAI,CAAC,QAAQ,MAAM,aAAa,WAAW,CAAC;AAC1H,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,MAAI,EAAE,WAAW,SAAS,MAAM,UAAU,UAAU,uBAAwB,IAAI,iBAAiB,OAAO;AACxG,MAAI,gBAAgB;AACpB,MAAI,kBAAkB;AACtB,WAAS,eAAe;AACpB,UAAM,kBAAkB,WAAW,EAAE,WAAW,OAAQ;AACxD,UAAM,eAAe,KAAK;AAC1B,UAAM,eAAe,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI;AAC9D,UAAM,sBAAsB,KAAK,KAAK,YAAY,IAAI,IAAI;AAC1D,QAAI,cAAc,QAAW;AACzB,kBAAY,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AAAA,IACvD;AACA,QAAI,eAAe,GAAG;AAClB,YAAM,cAAc,gBAAgB,qBAAqB,YAAY;AACrE,sBAAgB,CAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,eAAQ,KACJ,aACO,kBACC,eAAe,sBAAsB,gBACrC,cACA,KAAK,IAAI,cAAc,CAAC,IACxB,eAAe,KAAK,IAAI,cAAc,CAAC;AAAA,MACvD;AACA,wBAAkB,CAAC,MAAM;AACrB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,eAAQ,eACJ,sBACA,YACE,KAAK,IAAI,cAAc,CAAC,KACrB,kBACG,eACI,sBACA,gBACR,cACA,eAAe,KAAK,IAAI,cAAc,CAAC,KAC3C,YACK,KAAK,IAAI,cAAc,CAAC,KACpB,kBACG,eACI,sBACA,gBACR,cACI,eACA,KAAK,IAAI,cAAc,CAAC;AAAA,MAC5C;AAAA,IACJ,WACS,iBAAiB,GAAG;AACzB,sBAAgB,CAAC,MAAM,KACnB,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAC5B,gBACI,kBAAkB,sBAAsB,gBACrC;AAAA,IACpB,OACK;AACD,YAAM,oBAAoB,sBAAsB,KAAK,KAAK,eAAe,eAAe,CAAC;AACzF,sBAAgB,CAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,cAAM,WAAW,KAAK,IAAI,oBAAoB,GAAG,GAAG;AACpD,eAAQ,KACH,aACK,kBACE,eAAe,sBAAsB,gBACrC,KAAK,KAAK,QAAQ,IAClB,oBACI,eACA,KAAK,KAAK,QAAQ,KAC1B;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AACA,eAAa;AACb,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,UAAU,cAAc,CAAC;AAC/B,UAAI,CAAC,wBAAwB;AACzB,cAAM,kBAAkB,gBAAgB,CAAC,IAAI;AAC7C,cAAM,2BAA2B,KAAK,IAAI,eAAe,KAAK;AAC9D,cAAM,+BAA+B,KAAK,IAAI,KAAK,OAAO,KAAK;AAC/D,cAAM,OACF,4BAA4B;AAAA,MACpC,OACK;AACD,cAAM,OAAO,KAAK;AAAA,MACtB;AACA,YAAM,QAAQ,MAAM,OAAO,KAAK;AAChC,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AACd,iBAAW,CAAC;AACZ,OAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;AACtB,mBAAa;AAAA,IACjB;AAAA,EACJ;AACJ;AACA,OAAO,qBAAqB,CAACC,IAAGC,OAAM,OAAOD,OAAM,YAAY,OAAOC,OAAM;AAC5E,IAAM,OAAO,CAAC,OAAO;;;ACpHrB,IAAM,WAAW,CAAC,MAAM,IAAI,UAAU;AAClC,QAAM,mBAAmB,KAAK;AAC9B,SAAO,qBAAqB,IAAI,KAAK,QAAQ,QAAQ;AACzD;;;ACHA,IAAM,MAAM,CAAC,MAAM,IAAIC,cAAa,CAACA,YAAW,OAAOA,YAAW,KAAK;;;ACAvE,IAAMC,SAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACjE,IAAM,WAAW,CAAC,MAAO,IAAI,IAAI,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI;AACxD,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM;AACxB;;;ACLA,IAAM,SAAS;AAAA,EACX,MAAM,CAAC,MAAM,OAAO,MAAM;AAAA,EAC1B,OAAO;AAAA,EACP,WAAW,CAAC,MAAM;AACtB;AACA,IAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,WAAWC,OAAM,GAAG,CAAC,EAAE,CAAC;AACjF,IAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,EAAE,CAAC;;;ACNrE,IAAM,iBAAiB,CAAC,UAAU;AAAA,EAC9B,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,WAAW;AAAA,EACxE,OAAO;AAAA,EACP,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI;AACjC;AACA,IAAM,UAAU,eAAe,KAAK;AACpC,IAAM,UAAU,eAAe,GAAG;AAClC,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,qBAAqB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,OAAO,CAAC,MAAM,QAAQ,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,QAAQ,UAAU,IAAI,GAAG,EAAE,CAAC;;;ACV3J,IAAM,gBAAgB,CAAC,MAAM,aAAa,CAAC,MAAM;AAC7C,SAAO,QAAS,SAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC,KAAK,EAAE,WAAW,IAAI,KACvE,YAAY,OAAO,UAAU,eAAe,KAAK,GAAG,QAAQ,CAAE;AACvE;AACA,IAAM,aAAa,CAAC,OAAO,OAAO,UAAU,CAAC,MAAM;AAC/C,MAAI,CAAC,SAAS,CAAC;AACX,WAAO;AACX,QAAM,CAACC,IAAGC,IAAGC,IAAGC,MAAK,IAAI,EAAE,MAAM,UAAU;AAC3C,SAAO;AAAA,IACH,CAAC,KAAK,GAAG,WAAWH,EAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAWC,EAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAWC,EAAC;AAAA,IACrB,OAAOC,WAAU,SAAY,WAAWA,MAAK,IAAI;AAAA,EACrD;AACJ;;;ACXA,IAAM,OAAO;AAAA,EACT,MAAM,cAAc,OAAO,KAAK;AAAA,EAChC,OAAO,WAAW,OAAO,cAAc,WAAW;AAAA,EAClD,WAAW,CAAC,EAAE,KAAK,YAAY,WAAW,OAAO,UAAU,EAAE,MAAM;AAC/D,WAAQ,UACJ,KAAK,MAAM,GAAG,IACd,OACA,QAAQ,UAAU,SAAS,UAAU,CAAC,IACtC,OACA,QAAQ,UAAU,SAAS,SAAS,CAAC,IACrC,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AAAA,EACR;AACJ;;;ACfA,IAAM,eAAeC,OAAM,GAAG,GAAG;AACjC,IAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,WAAW,CAAC,MAAM,KAAK,MAAM,aAAa,CAAC,CAAC,EAAE,CAAC;AAC1G,IAAM,OAAO;AAAA,EACT,MAAM,cAAc,OAAO,KAAK;AAAA,EAChC,OAAO,WAAW,OAAO,SAAS,MAAM;AAAA,EACxC,WAAW,CAAC,EAAE,KAAK,OAAO,MAAM,OAAO,UAAU,EAAE,MAAM,UACrD,QAAQ,UAAU,GAAG,IACrB,OACA,QAAQ,UAAU,KAAK,IACvB,OACA,QAAQ,UAAU,IAAI,IACtB,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AACR;;;ACfA,SAAS,SAAS,GAAG;AACjB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAIC,KAAI;AACR,MAAIC,KAAI;AACR,MAAI,EAAE,SAAS,GAAG;AACd,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAD,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAC,KAAI,EAAE,OAAO,GAAG,CAAC;AAAA,EACrB,OACK;AACD,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAD,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAC,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,SAAK;AACL,SAAK;AACL,IAAAD,MAAKA;AACL,IAAAC,MAAKA;AAAA,EACT;AACA,SAAO;AAAA,IACH,KAAK,SAAS,GAAG,EAAE;AAAA,IACnB,OAAO,SAAS,GAAG,EAAE;AAAA,IACrB,MAAM,SAASD,IAAG,EAAE;AAAA,IACpB,OAAOC,KAAI,SAASA,IAAG,EAAE,IAAI,MAAM;AAAA,EACvC;AACJ;AACA,IAAM,MAAM;AAAA,EACR,MAAM,cAAc,GAAG;AAAA,EACvB,OAAO;AAAA,EACP,WAAW,KAAK;AACpB;;;AC9BA,IAAM,QAAQ;AAAA,EACV,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvD,OAAO,CAAC,MAAM;AACV,QAAI,KAAK,KAAK,CAAC,GAAG;AACd,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,KAAK,KAAK,CAAC,GAAG;AACnB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,OACK;AACD,aAAO,IAAI,MAAM,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,MAAM;AACd,WAAO,SAAS,CAAC,IACX,IACA,EAAE,eAAe,KAAK,IAClB,KAAK,UAAU,CAAC,IAChB,KAAK,UAAU,CAAC;AAAA,EAC9B;AACJ;;;ACrBA,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,SAAS,KAAK,GAAG;AACb,MAAIC,KAAI,IAAI,IAAI;AAChB,SAAQ,MAAM,CAAC,KACX,SAAS,CAAC,OACR,MAAMA,MAAK,EAAE,MAAM,UAAU,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK,EAAE,MAAM,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK,KAAK;AACxP;AACA,SAAS,QAAQ,GAAG;AAChB,MAAI,OAAO,MAAM;AACb,QAAI,GAAG,CAAC;AACZ,QAAM,SAAS,CAAC;AAChB,MAAI,YAAY;AAChB,QAAM,SAAS,EAAE,MAAM,UAAU;AACjC,MAAI,QAAQ;AACR,gBAAY,OAAO;AACnB,QAAI,EAAE,QAAQ,YAAY,UAAU;AACpC,WAAO,KAAK,GAAG,OAAO,IAAI,MAAM,KAAK,CAAC;AAAA,EAC1C;AACA,QAAM,UAAU,EAAE,MAAM,UAAU;AAClC,MAAI,SAAS;AACT,QAAI,EAAE,QAAQ,YAAY,WAAW;AACrC,WAAO,KAAK,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AAAA,EAC5C;AACA,SAAO,EAAE,QAAQ,WAAW,WAAW,EAAE;AAC7C;AACA,SAAS,MAAM,GAAG;AACd,SAAO,QAAQ,CAAC,EAAE;AACtB;AACA,SAAS,kBAAkB,GAAG;AAC1B,QAAM,EAAE,QAAQ,WAAW,UAAU,IAAI,QAAQ,CAAC;AAClD,QAAM,YAAY,OAAO;AACzB,SAAO,CAACC,OAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,eAAS,OAAO,QAAQ,IAAI,YAAY,aAAa,aAAa,IAAI,YAAY,MAAM,UAAUA,GAAE,CAAC,CAAC,IAAI,SAASA,GAAE,CAAC,CAAC,CAAC;AAAA,IAC5H;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,uBAAuB,CAAC,MAAM,OAAO,MAAM,WAAW,IAAI;AAChE,SAAS,kBAAkB,GAAG;AAC1B,QAAM,SAAS,MAAM,CAAC;AACtB,QAAM,cAAc,kBAAkB,CAAC;AACvC,SAAO,YAAY,OAAO,IAAI,oBAAoB,CAAC;AACvD;AACA,IAAM,UAAU,EAAE,MAAM,OAAO,mBAAmB,kBAAkB;;;AC/CpE,IAAM,cAAc,oBAAI,IAAI,CAAC,cAAc,YAAY,YAAY,SAAS,CAAC;AAC7E,SAAS,mBAAmB,GAAG;AAC3B,MAAI,CAAC,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC5C,MAAI,SAAS;AACT,WAAO;AACX,QAAM,CAACC,OAAM,IAAI,MAAM,MAAM,UAAU,KAAK,CAAC;AAC7C,MAAI,CAACA;AACD,WAAO;AACX,QAAM,OAAO,MAAM,QAAQA,SAAQ,EAAE;AACrC,MAAI,eAAe,YAAY,IAAI,IAAI,IAAI,IAAI;AAC/C,MAAIA,YAAW;AACX,oBAAgB;AACpB,SAAO,OAAO,MAAM,eAAe,OAAO;AAC9C;AACA,IAAM,gBAAgB;AACtB,IAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,mBAAmB,CAAC,MAAM;AAC7E,QAAM,YAAY,EAAE,MAAM,aAAa;AACvC,SAAO,YAAY,UAAU,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAAI;AACrE,EAAE,CAAC;;;ACrBP,SAAS,SAAS,GAAG,GAAG,GAAG;AACvB,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,IAAI,IAAI;AACR,WAAO;AACX,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AACvC,SAAO;AACX;AACA,SAAS,WAAW,EAAE,KAAK,YAAY,WAAW,OAAAC,OAAM,GAAG;AACvD,SAAO;AACP,gBAAc;AACd,eAAa;AACb,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,CAAC,YAAY;AACb,UAAM,QAAQ,OAAO;AAAA,EACzB,OACK;AACD,UAAM,IAAI,YAAY,MAChB,aAAa,IAAI,cACjB,YAAY,aAAa,YAAY;AAC3C,UAAM,IAAI,IAAI,YAAY;AAC1B,UAAM,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAChC,YAAQ,SAAS,GAAG,GAAG,GAAG;AAC1B,WAAO,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACH,KAAK,KAAK,MAAM,MAAM,GAAG;AAAA,IACzB,OAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,IAC7B,MAAM,KAAK,MAAM,OAAO,GAAG;AAAA,IAC3B,OAAAA;AAAA,EACJ;AACJ;;;ACjCA,IAAM,iBAAiB,CAAC,MAAM,IAAI,MAAM;AACpC,QAAM,WAAW,OAAO;AACxB,QAAM,SAAS,KAAK;AACpB,SAAO,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,SAAS,YAAY,QAAQ,CAAC;AACpE;AACA,IAAM,aAAa,CAAC,KAAK,MAAM,IAAI;AACnC,IAAM,eAAe,CAAC,MAAM,WAAW,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;AAClE,IAAM,gBAAgB,CAACC,WAAU,IAAIA,MAAK;AAC1C,IAAM,WAAW,CAAC,MAAM,OAAO;AAC3B,MAAI,gBAAgB,aAAa,IAAI;AACrC,MAAI,cAAc,aAAa,EAAE;AACjC,YAAU,CAAC,CAAC,eAAe,cAAc,IAAI,CAAC;AAC9C,YAAU,CAAC,CAAC,aAAa,cAAc,EAAE,CAAC;AAC1C,MAAI,YAAY,cAAc,MAAM,IAAI;AACxC,MAAI,UAAU,YAAY,MAAM,EAAE;AAClC,MAAI,kBAAkB,MAAM;AACxB,gBAAY,WAAW,SAAS;AAChC,oBAAgB;AAAA,EACpB;AACA,MAAI,gBAAgB,MAAM;AACtB,cAAU,WAAW,OAAO;AAC5B,kBAAc;AAAA,EAClB;AACA,QAAM,UAAU,OAAO,OAAO,CAAC,GAAG,SAAS;AAC3C,SAAO,CAAC,MAAM;AACV,eAAW,OAAO,SAAS;AACvB,UAAI,QAAQ,SAAS;AACjB,gBAAQ,GAAG,IAAI,eAAe,UAAU,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC;AAAA,MACjE;AAAA,IACJ;AACA,YAAQ,QAAQ,IAAI,UAAU,OAAO,QAAQ,OAAO,CAAC;AACrD,WAAO,cAAc,UAAU,OAAO;AAAA,EAC1C;AACJ;;;ACjCA,IAAM,QAAQ,CAAC,MAAM,OAAO,MAAM;;;ACLlC,IAAM,mBAAmB,CAACC,IAAGC,OAAM,CAAC,MAAMA,GAAED,GAAE,CAAC,CAAC;AAChD,IAAM,OAAO,IAAI,iBAAiB,aAAa,OAAO,gBAAgB;;;ACMtE,SAAS,SAAS,QAAQ,QAAQ;AAC9B,MAAI,MAAM,MAAM,GAAG;AACf,WAAO,CAAC,MAAM,IAAI,QAAQ,QAAQ,CAAC;AAAA,EACvC,WACS,MAAM,KAAK,MAAM,GAAG;AACzB,WAAO,SAAS,QAAQ,MAAM;AAAA,EAClC,OACK;AACD,WAAO,WAAW,QAAQ,MAAM;AAAA,EACpC;AACJ;AACA,IAAM,WAAW,CAAC,MAAM,OAAO;AAC3B,QAAM,SAAS,CAAC,GAAG,IAAI;AACvB,QAAM,YAAY,OAAO;AACzB,QAAM,aAAa,KAAK,IAAI,CAAC,UAAU,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,CAAC;AACtE,SAAO,CAAC,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,aAAO,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,YAAY,CAAC,QAAQ,WAAW;AAClC,QAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,MAAM;AAC9D,QAAM,aAAa,CAAC;AACpB,aAAW,OAAO,QAAQ;AACtB,QAAI,OAAO,GAAG,MAAM,UAAa,OAAO,GAAG,MAAM,QAAW;AACxD,iBAAW,GAAG,IAAI,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IACvD;AAAA,EACJ;AACA,SAAO,CAAC,MAAM;AACV,eAAW,OAAO,YAAY;AAC1B,aAAO,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAASE,SAAQ,OAAO;AACpB,QAAM,SAAS,QAAQ,MAAM,KAAK;AAClC,QAAM,YAAY,OAAO;AACzB,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,cAAc,OAAO,OAAO,CAAC,MAAM,UAAU;AAC7C;AAAA,IACJ,OACK;AACD,UAAI,OAAO,CAAC,EAAE,QAAQ,QAAW;AAC7B;AAAA,MACJ,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,YAAY,QAAQ,OAAO;AAChD;AACA,IAAM,aAAa,CAAC,QAAQ,WAAW;AACnC,QAAM,WAAW,QAAQ,kBAAkB,MAAM;AACjD,QAAM,cAAcA,SAAQ,MAAM;AAClC,QAAM,cAAcA,SAAQ,MAAM;AAClC,QAAM,iBAAiB,YAAY,WAAW,YAAY,UACtD,YAAY,WAAW,YAAY,UACnC,YAAY,cAAc,YAAY;AAC1C,MAAI,gBAAgB;AAChB,WAAO,KAAK,SAAS,YAAY,QAAQ,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC1E,OACK;AACD,YAAQ,MAAM,mBAAmB,MAAM,UAAU,MAAM,0KAA0K;AACjO,WAAO,CAAC,MAAM,GAAG,IAAI,IAAI,SAAS,MAAM;AAAA,EAC5C;AACJ;;;ACtEA,IAAM,YAAY,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC;AACtD,SAAS,mBAAmB,GAAG;AAC3B,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,QAAI,MAAM,KAAK,CAAC,GAAG;AACf,aAAO;AAAA,IACX,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,QAAQ,MAAM,aAAa;AAC7C,QAAM,SAAS,CAAC;AAChB,QAAM,eAAe,eAAe,mBAAmB,OAAO,CAAC,CAAC;AAChE,QAAM,YAAY,OAAO,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,QAAQ,aAAa,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACjD,QAAI,MAAM;AACN,YAAM,iBAAiB,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AACvD,cAAQ,KAAK,gBAAgB,KAAK;AAAA,IACtC;AACA,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG;AAC1C,SAAO,CAAC,MAAM,MAAM,SAAS,MAAM,IAAI,CAAC,CAAC;AAC7C;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACpC,QAAM,cAAc,MAAM;AAC1B,QAAM,iBAAiB,cAAc;AACrC,SAAO,CAAC,MAAM;AACV,QAAI,aAAa;AACjB,QAAI,kBAAkB;AACtB,QAAI,KAAK,MAAM,CAAC,GAAG;AACf,wBAAkB;AAAA,IACtB,WACS,KAAK,MAAM,cAAc,GAAG;AACjC,mBAAa,iBAAiB;AAC9B,wBAAkB;AAAA,IACtB;AACA,QAAI,CAAC,iBAAiB;AAClB,UAAI,IAAI;AACR,aAAO,IAAI,aAAa,KAAK;AACzB,YAAI,MAAM,CAAC,IAAI,KAAK,MAAM,gBAAgB;AACtC;AAAA,QACJ;AAAA,MACJ;AACA,mBAAa,IAAI;AAAA,IACrB;AACA,UAAM,kBAAkB,SAAS,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC;AAC5E,WAAO,OAAO,UAAU,EAAE,eAAe;AAAA,EAC7C;AACJ;AACA,SAAS,YAAY,OAAO,QAAQ,EAAE,OAAO,UAAU,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG;AAC7E,QAAM,cAAc,MAAM;AAC1B,YAAU,gBAAgB,OAAO,QAAQ,sDAAsD;AAC/F,YAAU,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,KAAK,KAAK,WAAW,cAAc,GAAG,kIAAkI;AAC9M,MAAI,MAAM,CAAC,IAAI,MAAM,cAAc,CAAC,GAAG;AACnC,YAAQ,CAAC,EAAE,OAAO,KAAK;AACvB,aAAS,CAAC,EAAE,OAAO,MAAM;AACzB,UAAM,QAAQ;AACd,WAAO,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,aAAa,QAAQ,MAAM,KAAK;AAC/C,QAAM,eAAe,gBAAgB,IAC/B,gBAAgB,OAAO,MAAM,IAC7B,gBAAgB,OAAO,MAAM;AACnC,SAAO,UACD,CAAC,MAAM,aAAaC,OAAM,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,IAC9D;AACV;;;ACzFA,IAAM,gBAAgB,YAAU,OAAK,IAAI,OAAO,IAAI,CAAC;AACrD,IAAM,eAAe,YAAU,OAAK,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK;AAC/F,IAAM,eAAe,CAAC,UAAU,OAAK,KAAK,IAAI,GAAG,KAAK;AACtD,IAAM,eAAe,CAAC,UAAU,OAAK,IAAI,MAAM,QAAQ,KAAK,IAAI;AAChE,IAAM,mBAAmB,CAAC,UAAU;AAChC,QAAM,aAAa,aAAa,KAAK;AACrC,SAAO,QAAM,KAAK,KAAK,IACjB,MAAM,WAAW,CAAC,IAClB,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE;AAC9C;;;ACPA,IAAM,6BAA6B;AACnC,IAAM,yBAAyB,IAAM;AACrC,IAAM,0BAA0B,IAAM;AACtC,IAAM,yBAAyB,IAAM;AACrC,IAAM,SAAS,OAAK;AACpB,IAAM,SAAS,aAAa,CAAC;AAC7B,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,MAAM;AACrC,IAAM,SAAS,OAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAC7C,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,OAAO;AACtC,IAAM,SAAS,aAAa,0BAA0B;AACtD,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,MAAM;AACrC,IAAM,aAAa,iBAAiB,0BAA0B;AAC9D,IAAM,KAAK,OAAS;AACpB,IAAM,KAAK,QAAU;AACrB,IAAM,KAAK,QAAU;AACrB,IAAM,YAAY,CAAC,MAAM;AACrB,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,QAAM,KAAK,IAAI;AACf,SAAO,IAAI,yBACL,SAAS,KACT,IAAI,0BACA,QAAQ,KAAK,MAAM,IAAI,MACvB,IAAI,yBACA,KAAK,KAAK,KAAK,IAAI,KACnB,OAAO,IAAI,IAAI,QAAQ,IAAI;AAC7C;AACA,IAAM,WAAW,cAAc,SAAS;AACxC,IAAM,cAAc,CAAC,MAAM,IAAI,MACzB,OAAO,IAAM,UAAU,IAAM,IAAI,CAAG,KACpC,MAAM,UAAU,IAAI,IAAM,CAAG,IAAI;;;AChCvC,SAAS,cAAc,QAAQ,QAAQ;AACnC,SAAO,OAAO,IAAI,MAAM,UAAU,SAAS,EAAE,OAAO,GAAG,OAAO,SAAS,CAAC;AAC5E;AACA,SAAS,cAAc,QAAQ;AAC3B,QAAM,YAAY,OAAO;AACzB,SAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,MAAM,IAAI,KAAK,YAAY,KAAK,CAAC;AACtE;AACA,SAAS,qBAAqB,QAAQ,UAAU;AAC5C,SAAO,OAAO,IAAI,CAAC,MAAM,IAAI,QAAQ;AACzC;AACA,SAAS,UAAU,EAAE,OAAO,GAAG,KAAK,GAAG,MAAM,QAAQ,WAAW,IAAK,GAAG;AACpE,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,QAAM,SAAS,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE;AACjD,QAAM,QAAQ,qBAAqB,UAAU,OAAO,WAAW,OAAO,SAChE,SACA,cAAc,MAAM,GAAG,QAAQ;AACrC,WAAS,qBAAqB;AAC1B,WAAO,YAAY,OAAO,QAAQ;AAAA,MAC9B,MAAM,MAAM,QAAQ,IAAI,IAAI,OAAO,cAAc,QAAQ,IAAI;AAAA,IACjE,CAAC;AAAA,EACL;AACA,MAAI,eAAe,mBAAmB;AACtC,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,QAAQ,aAAa,CAAC;AAC5B,YAAM,OAAO,KAAK;AAClB,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AACd,aAAO,QAAQ;AACf,qBAAe,mBAAmB;AAAA,IACtC;AAAA,EACJ;AACJ;;;ACpCA,SAAS,MAAM,EAAE,WAAW,GAAG,OAAO,GAAG,QAAQ,KAAK,eAAe,KAAK,YAAY,KAAK,aAAc,GAAG;AACxG,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,MAAI,YAAY,QAAQ;AACxB,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,iBAAiB,SAAY,QAAQ,aAAa,KAAK;AACtE,MAAI,WAAW;AACX,gBAAY,SAAS;AACzB,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,YAAY;AACrD,YAAM,OAAO,EAAE,QAAQ,aAAa,QAAQ,CAAC;AAC7C,YAAM,QAAQ,MAAM,OAAO,SAAS,SAAS;AAC7C,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AAAA,IAAE;AAAA,EACxB;AACJ;;;ACZA,IAAM,QAAQ,EAAE,WAAW,QAAQ,MAAM;AACzC,SAAS,2BAA2B,QAAQ;AACxC,MAAI,MAAM,QAAQ,OAAO,EAAE,GAAG;AAC1B,WAAO;AAAA,EACX,WACS,MAAM,OAAO,IAAI,GAAG;AACzB,WAAO,MAAM,OAAO,IAAI;AAAA,EAC5B;AACA,QAAMC,QAAO,IAAI,IAAI,OAAO,KAAK,MAAM,CAAC;AACxC,MAAIA,MAAK,IAAI,MAAM,KACdA,MAAK,IAAI,UAAU,KAAK,CAACA,MAAK,IAAI,cAAc,GAAI;AACrD,WAAO;AAAA,EACX,WACSA,MAAK,IAAI,cAAc,KAC5BA,MAAK,IAAI,WAAW,KACpBA,MAAK,IAAI,MAAM,KACfA,MAAK,IAAI,SAAS,KAClBA,MAAK,IAAI,WAAW,KACpBA,MAAK,IAAI,WAAW,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AC1BA,SAAS,YAAY,SAAS,UAAU,QAAQ,GAAG;AAC/C,SAAO,UAAU,WAAW;AAChC;AACA,SAAS,eAAe,SAAS,UAAU,QAAQ,GAAG,oBAAoB,MAAM;AAC5E,SAAO,oBACD,YAAY,WAAW,CAAC,SAAS,UAAU,KAAK,IAChD,YAAY,UAAU,YAAY;AAC5C;AACA,SAAS,sBAAsB,SAAS,UAAU,OAAO,mBAAmB;AACxE,SAAO,oBAAoB,WAAW,WAAW,QAAQ,WAAW,CAAC;AACzE;;;ACJA,IAAM,YAAY,CAAC,WAAW;AAC1B,QAAM,gBAAgB,CAAC,EAAE,MAAM,MAAM,OAAO,KAAK;AACjD,SAAO;AAAA,IACH,OAAO,MAAM,WAAK,OAAO,eAAe,IAAI;AAAA,IAC5C,MAAM,MAAM,WAAW,OAAO,aAAa;AAAA,EAC/C;AACJ;AACA,SAAS,QAAQC,KAAI;AACjB,MAAI,IAAI;AACR,MAAI,EAAE,MAAM,WAAW,MAAM,SAAS,WAAW,UAAU,GAAG,QAAQ,YAAY,GAAG,aAAa,QAAQ,cAAc,GAAG,QAAQ,QAAQ,YAAY,UAAU,SAAS,IAAIA,KAAI,UAAU,OAAOA,KAAI,CAAC,QAAQ,YAAY,UAAU,WAAW,UAAU,cAAc,eAAe,UAAU,UAAU,cAAc,YAAY,UAAU,CAAC;AACjV,MAAI,EAAE,GAAG,IAAI;AACb,MAAI;AACJ,MAAI,cAAc;AAClB,MAAI,mBAAmB,QAAQ;AAC/B,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,oBAAoB;AACxB,MAAI;AACJ,QAAM,WAAW,2BAA2B,OAAO;AACnD,OAAK,MAAM,KAAK,UAAU,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,MAAM,EAAE,GAAG;AACtG,4BAAwB,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG;AAAA,MACtD,OAAO;AAAA,IACX,CAAC;AACD,WAAO;AACP,SAAK;AAAA,EACT;AACA,QAAM,YAAY,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC;AAClF,WAAS,SAAS;AACd;AACA,QAAI,eAAe,WAAW;AAC1B,0BAAoB,cAAc,MAAM;AACxC,gBAAU,eAAe,SAAS,kBAAkB,aAAa,iBAAiB;AAAA,IACtF,OACK;AACD,gBAAU,YAAY,SAAS,kBAAkB,WAAW;AAC5D,UAAI,eAAe;AACf,kBAAU,WAAW;AAAA,IAC7B;AACA,iBAAa;AACb,gBAAY,SAAS;AAAA,EACzB;AACA,WAAS,WAAW;AAChB,mBAAe,KAAK;AACpB,kBAAc,WAAW;AAAA,EAC7B;AACA,WAAS,OAAO,OAAO;AACnB,QAAI,CAAC;AACD,cAAQ,CAAC;AACb,eAAW;AACX,QAAI,CAAC,YAAY;AACb,YAAM,QAAQ,UAAU,KAAK,KAAK,IAAI,GAAG,OAAO,CAAC;AACjD,eAAS,MAAM;AACf,UAAI;AACA,iBAAS,sBAAsB,MAAM;AACzC,mBAAa,oBAAoB,MAAM,OAAO,WAAW;AAAA,IAC7D;AACA,iBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,MAAM;AACnE,QAAI,YAAY;AACZ,UAAI,gBAAgB;AAChB,6BAAqB,QAAQ,qBAAqB,SAAS,mBAAoB,mBAAmB;AACtG,UAAI,cAAc,WAAW;AACzB,8BAAsB,SAAS,kBAAkB,aAAa,iBAAiB,KAAK,OAAO;AAAA,MAC/F,OACK;AACD,iBAAS;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,OAAO;AACZ,eAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvD,qBAAiB,OAAO,MAAM;AAC9B,mBAAe,MAAM;AAAA,EACzB;AACA,cAAY,KAAK;AACjB,SAAO;AAAA,IACH,MAAM,MAAM;AACR,iBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvD,qBAAe,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACtFA,SAAS,kBAAkB,UAAU,eAAe;AAChD,SAAO,gBAAgB,YAAY,MAAO,iBAAiB;AAC/D;;;ACEA,SAAS,QAAQ,EAAE,OAAO,GAAG,WAAW,GAAG,KAAK,KAAK,QAAQ,KAAK,eAAe,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,YAAY,GAAG,cAAc,QAAQ,UAAU,YAAY,OAAQ,GAAG;AACnM,MAAI;AACJ,WAAS,cAAc,GAAG;AACtB,WAAQ,QAAQ,UAAa,IAAI,OAAS,QAAQ,UAAa,IAAI;AAAA,EACvE;AACA,WAAS,gBAAgB,GAAG;AACxB,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ;AACR,aAAO;AACX,WAAO,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM;AAAA,EACzD;AACA,WAAS,eAAe,SAAS;AAC7B,yBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK;AAC1F,uBAAmB,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,MAAE;AAAA,MAAQ,UAAU,CAAC,MAAM;AACxF,YAAIC;AACJ,qBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,CAAC;AAC9D,SAACA,MAAK,QAAQ,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,SAAS,CAAC;AAAA,MACnF;AAAA,MAAG;AAAA,MACH;AAAA,IAAO,CAAC,CAAC;AAAA,EACjB;AACA,WAAS,YAAY,SAAS;AAC1B,mBAAe,OAAO,OAAO,EAAE,MAAM,UAAU,WAAW,iBAAiB,SAAS,eAAe,UAAU,GAAG,OAAO,CAAC;AAAA,EAC5H;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY,EAAE,MAAM,UAAU,IAAI,gBAAgB,IAAI,EAAE,CAAC;AAAA,EAC7D,OACK;AACD,QAAI,SAAS,QAAQ,WAAW;AAChC,QAAI,OAAO,iBAAiB;AACxB,eAAS,aAAa,MAAM;AAChC,UAAM,WAAW,gBAAgB,MAAM;AACvC,UAAM,UAAU,aAAa,MAAM,KAAK;AACxC,QAAI;AACJ,QAAI;AACJ,UAAM,gBAAgB,CAAC,MAAM;AACzB,aAAO;AACP,gBAAU;AACV,iBAAW,kBAAkB,IAAI,MAAM,aAAa,EAAE,KAAK;AAC3D,UAAK,YAAY,KAAK,IAAI,YACrB,YAAY,MAAM,IAAI,UAAW;AAClC,oBAAY,EAAE,MAAM,GAAG,IAAI,UAAU,SAAS,CAAC;AAAA,MACnD;AAAA,IACJ;AACA,mBAAe;AAAA,MACX,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,cAAc,MAAM,IAAI,gBAAgB;AAAA,IACtD,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH,MAAM,MAAM,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK;AAAA,EAC1G;AACJ;;;AC9DA,IAAMC,YAAW,CAAC,MAAM;AACxB,IAAM,kBAAkB,CAAC,oBAAoBA,cAAa,CAAC,UAAU,QAAQ,MAAM;AAC/E,QAAM,eAAe,SAAS;AAC9B,QAAM,6BAA6B,EAAE,IAAI,WAAW,MAAM,IAAI,kBAAkB,KAAK,IAAI,YAAY,CAAC;AACtG,SAAO,gBAAgB,IACjB,SAAS,6BACT,SAAS;AACnB;AACA,IAAM,UAAU,gBAAgB;AAChC,IAAM,cAAc,gBAAgB,KAAK,IAAI;;;ACP7C,IAAM,IAAI,CAAC,IAAI,OAAO,IAAM,IAAM,KAAK,IAAM;AAC7C,IAAM,IAAI,CAAC,IAAI,OAAO,IAAM,KAAK,IAAM;AACvC,IAAM,IAAI,CAAC,OAAO,IAAM;AACxB,IAAM,aAAa,CAAC,GAAG,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK;AAC9E,IAAM,WAAW,CAAC,GAAG,IAAI,OAAO,IAAM,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAM,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;AACpF,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,SAAS,gBAAgB,IAAI,IAAI,IAAI,KAAK,KAAK;AAC3C,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI;AACR,KAAG;AACC,eAAW,MAAM,KAAK,MAAM;AAC5B,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAChB,WAAK;AAAA,IACT,OACK;AACD,WAAK;AAAA,IACT;AAAA,EACJ,SAAS,KAAK,IAAI,QAAQ,IAAI,wBAC1B,EAAE,IAAI;AACV,SAAO;AACX;AACA,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,SAAS,qBAAqB,IAAI,SAAS,KAAK,KAAK;AACjD,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACvC,UAAM,eAAe,SAAS,SAAS,KAAK,GAAG;AAC/C,QAAI,iBAAiB,GAAK;AACtB,aAAO;AAAA,IACX;AACA,UAAM,WAAW,WAAW,SAAS,KAAK,GAAG,IAAI;AACjD,eAAW,WAAW;AAAA,EAC1B;AACA,SAAO;AACX;AACA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB,KAAO,mBAAmB;AAClD,SAAS,YAAY,KAAK,KAAK,KAAK,KAAK;AACrC,MAAI,QAAQ,OAAO,QAAQ;AACvB,WAAO;AACX,QAAM,eAAe,IAAI,aAAa,gBAAgB;AACtD,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACvC,iBAAa,CAAC,IAAI,WAAW,IAAI,iBAAiB,KAAK,GAAG;AAAA,EAC9D;AACA,WAAS,SAAS,IAAI;AAClB,QAAI,gBAAgB;AACpB,QAAI,gBAAgB;AACpB,UAAM,aAAa,mBAAmB;AACtC,WAAO,kBAAkB,cAAc,aAAa,aAAa,KAAK,IAAI,EAAE,eAAe;AACvF,uBAAiB;AAAA,IACrB;AACA,MAAE;AACF,UAAM,QAAQ,KAAK,aAAa,aAAa,MACxC,aAAa,gBAAgB,CAAC,IAAI,aAAa,aAAa;AACjE,UAAM,YAAY,gBAAgB,OAAO;AACzC,UAAM,eAAe,SAAS,WAAW,KAAK,GAAG;AACjD,QAAI,gBAAgB,gBAAgB;AAChC,aAAO,qBAAqB,IAAI,WAAW,KAAK,GAAG;AAAA,IACvD,WACS,iBAAiB,GAAK;AAC3B,aAAO;AAAA,IACX,OACK;AACD,aAAO,gBAAgB,IAAI,eAAe,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,IACvF;AAAA,EACJ;AACA,SAAO,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAC3E;;;AC/DA,IAAM,cAAc,CAAC;AAErB,IAAM,sBAAN,MAA0B;AAAA,EAA1B;AACE,yCAAgC,oBAAI,IAAI;AAAA;AAAA,EACxC,IAAI,SAAS;AACX,SAAK,cAAc,IAAI,OAAO;AAC9B,WAAO,MAAM,KAAK,cAAc,OAAO,OAAO;AAAA,EAChD;AAAA,EACA,OAAOC,IAAGC,IAAGC,IAAG;AACd,QAAI,CAAC,KAAK,cAAc;AACtB;AACF,eAAW,WAAW,KAAK,cAAe,SAAQF,IAAGC,IAAGC,EAAC;AAAA,EAC3D;AAAA,EACA,QAAQ;AACN,SAAK,cAAc,MAAM;AAAA,EAC3B;AACF;AAEA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,OAAO,MAAM,OAAO,WAAW,KAAK,CAAC;AAC/C;AACA,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAiChB,YAAY,MAAM;AA7BlB;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA,qCAAY;AAIZ;AAAA;AAAA;AAAA,uCAAc;AAId;AAAA;AAAA;AAAA,6CAAoB,IAAI,oBAAoB;AAI5C;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA,4CAAmB;AAmCnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAAkB,CAAC,MAAM;AACvB,WAAK,OAAO,KAAK;AACjB,WAAK,UAAU;AACf,YAAM,EAAE,OAAO,WAAAC,WAAU,IAAI,aAAa;AAC1C,UAAI,KAAK,gBAAgBA,YAAW;AAClC,aAAK,YAAY;AACjB,aAAK,cAAcA;AAAA,MACrB;AACA,iBAAK,WAAW,KAAK,qBAAqB;AAC1C,WAAK,kBAAkB,OAAO,KAAK,OAAO;AAAA,IAC5C;AA4BA;AAAA;AAAA;AAAA,iDAAwB,MAAM,WAAK,WAAW,KAAK,aAAa;AAKhE;AAAA;AAAA;AAAA;AAAA,yCAAgB,CAAC,EAAE,WAAAA,WAAU,MAAM;AACjC,UAAI,CAAC,KAAK;AACR,aAAK,mBAAmB,QAAQ,KAAK,OAAO;AAC9C,UAAIA,eAAc,KAAK;AACrB,aAAK,OAAO,KAAK;AAAA,IACrB;AA7EE,SAAK,OAAO,KAAK,UAAU;AAC3B,SAAK,mBAAmB,QAAQ,KAAK,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,cAAc;AACrB,WAAO,KAAK,kBAAkB,IAAI,YAAY;AAAA,EAChD;AAAA,EACA,iBAAiB;AACf,SAAK,kBAAkB,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,GAAG;AACL,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM;AACJ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,mBAAmB,kBAAkB,OAAO,WAAW,KAAK,OAAO,IAAI,OAAO,WAAW,KAAK,IAAI,GAAG,KAAK,SAAS,IAAI;AAAA,EACrI;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,WAAW;AACf,SAAK,KAAK;AACV,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM,EAAE,KAAK,IAAI,UAAU,OAAO;AAClC,WAAK,gBAAgB;AAAA,IACvB,CAAC,EAAE,KAAK,MAAM,KAAK,eAAe,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,QAAI,KAAK;AACP,WAAK,cAAc;AACrB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,kBAAkB,MAAM;AAC7B,SAAK,KAAK;AAAA,EACZ;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,IAAI,YAAY,IAAI;AAC7B;AAEA,IAAM,EAAE,QAAQ,IAAI;AACpB,SAAS,kBAAkB;AACzB,QAAM,eAAe,IAAI,CAAC,CAAC;AAC3B,QAAM,OAAO,CAACC,UAAS;AACrB,UAAM,aAAa,CAAC,QAAQ;AAC1B,UAAI,CAAC,aAAa,MAAM,GAAG;AACzB;AACF,mBAAa,MAAM,GAAG,EAAE,KAAK;AAC7B,mBAAa,MAAM,GAAG,EAAE,QAAQ;AAChC,aAAO,aAAa,MAAM,GAAG;AAAA,IAC/B;AACA,QAAIA,OAAM;AACR,UAAI,QAAQA,KAAI,GAAG;AACjB,QAAAA,MAAK,QAAQ,UAAU;AAAA,MACzB,OAAO;AACL,mBAAWA,KAAI;AAAA,MACjB;AAAA,IACF,OAAO;AACL,aAAO,KAAK,aAAa,KAAK,EAAE,QAAQ,UAAU;AAAA,IACpD;AAAA,EACF;AACA,QAAM,MAAM,CAAC,KAAK,MAAM,WAAW;AACjC,QAAI,aAAa,MAAM,GAAG;AACxB,aAAO,aAAa,MAAM,GAAG;AAC/B,UAAM,cAAc,eAAe,IAAI;AACvC,gBAAY,SAAS,CAAC,MAAM,OAAO,GAAG,IAAI,CAAC;AAC3C,iBAAa,MAAM,GAAG,IAAI;AAC1B,WAAO;AAAA,EACT;AACA,iBAAe,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,MAAM,QAAQ,CAAC;AACxB;AACA,SAAS,oBAAoB;AAC3B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,uBAAuB,IAAI;AAClC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,OAAO,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAAA,IACzC,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,iBAAiB,IAAI;AAC5B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,OAAO,IAAI,MAAM;AAAA,IAC1B,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,cAAc;AACrB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AACF;AACA,SAASC,WAAU,QAAQ;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,SAAS;AACX;AACA,SAAS,qBAAqB,UAAU,IAAI;AAC1C,MAAI;AACJ,MAAI,kBAAkB,EAAE,GAAG;AACzB,wBAAoBA;AAAA,EACtB,OAAO;AACL,wBAAoB,mBAAmB,QAAQ,KAAK,mBAAmB;AAAA,EACzE;AACA,SAAO,EAAE,IAAI,GAAG,kBAAkB,EAAE,EAAE;AACxC;AAEA,IAAM,MAAM;AAAA,EACV,GAAG;AAAA,EACH,WAAW,KAAK;AAClB;AACA,IAAM,aAAa;AAAA;AAAA,EAEjB;AAAA,EACA,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,wBAAwB;AAAA;AAAA,EAExB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA;AAAA,EAEZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,QAAQ;AAAA,EACR;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AACd;AACA,IAAM,eAAe,CAAC,QAAQ,WAAW,GAAG;AAC5C,SAAS,eAAe,OAAO,MAAM;AACnC,SAAO,QAAQ,OAAO,UAAU,YAAY,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACvF;AACA,SAASC,mBAAkB,KAAK,OAAO;AACrC,MAAI,mBAAmB,aAAa,GAAG;AACvC,MAAI,qBAAqB;AACvB,uBAAmB;AACrB,SAAO,iBAAiB,oBAAoB,iBAAiB,kBAAkB,KAAK,IAAI;AAC1F;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,2BAA2B,YAAY;AAC9C,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,WAAO,YAAY,IAAI,IAAI,IAAI,EAAE;AAAA,EACnC,WAAW,OAAO,eAAe,UAAU;AACzC,WAAO,aAAa,UAAU;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,MAAM,QAAQ,IAAI,KAAK,OAAO,KAAK,CAAC,MAAM;AACnD;AACA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,QAAQ;AACV,WAAO;AACT,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK;AAClD,WAAO;AACT,MAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,KAAK,KAAK,CAAC,MAAM,WAAW,MAAM,GAAG;AACjF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,MAAM,QAAQ,QAAQ,EAAE,KAAK,QAAQ,GAAG,CAAC,MAAM,MAAM;AACvD,YAAQ,KAAK,CAAC,GAAG,QAAQ,EAAE;AAC3B,YAAQ,GAAG,CAAC,IAAI,QAAQ;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,oCAAoC,EAAE,MAAM,OAAO,OAAO,GAAG,WAAW,GAAG;AAClF,QAAM,UAAU,EAAE,GAAG,WAAW;AAChC,MAAI;AACF,YAAQ,SAAS;AACnB,MAAI,MAAM;AACR,YAAQ,OAAO,cAAc,IAAI,IAAI,KAAK,IAAI,0BAA0B,IAAI,2BAA2B,IAAI;AAAA,EAC7G;AACA,MAAI;AACF,YAAQ,UAAU,CAAC;AACrB,SAAO;AACT;AACA,SAAS,6BAA6B,YAAY,SAAS,KAAK;AAC9D,MAAI,MAAM,QAAQ,QAAQ,EAAE,GAAG;AAC7B,QAAI,CAAC,WAAW;AACd,iBAAW,WAAW;AAAA,EAC1B;AACA,mBAAiB,OAAO;AACxB,MAAI,CAAC,oBAAoB,UAAU,GAAG;AACpC,iBAAa;AAAA,MACX,GAAG;AAAA,MACH,GAAG,qBAAqB,KAAK,QAAQ,EAAE;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,oCAAoC,UAAU;AAAA,EACnD;AACF;AACA,SAAS,oBAAoB,EAAE,OAAO,QAAQ,YAAY,aAAa,MAAM,GAAG,WAAW,GAAG;AAC5F,SAAO,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;AACnC;AACA,SAAS,mBAAmB,YAAY,KAAK;AAC3C,SAAO,WAAW,GAAG,KAAK,WAAW,WAAW;AAClD;AACA,SAAS,aAAa,KAAK,OAAO,QAAQ,YAAY,YAAY;AAChE,QAAM,kBAAkB,mBAAmB,YAAY,GAAG;AAC1D,MAAI,SAAS,gBAAgB,SAAS,QAAQ,gBAAgB,SAAS,SAAS,MAAM,IAAI,IAAI,gBAAgB;AAC9G,QAAM,qBAAqB,aAAa,KAAK,MAAM;AACnD,MAAI,WAAW,UAAU,sBAAsB,OAAO,WAAW;AAC/D,aAASA,mBAAkB,KAAK,MAAM;AACxC,QAAM,qBAAqB,aAAa,KAAK,MAAM;AACnD,WAAS,MAAM,UAAU;AACvB,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,UAAU,WAAW,WAAW,WAAW,WAAW,MAAM,YAAY;AAAA,MACxE,UAAU,CAAC,MAAM,MAAM,IAAI,CAAC;AAAA,IAC9B;AACA,WAAO,gBAAgB,SAAS,aAAa,gBAAgB,SAAS,UAAU,QAAQ,EAAE,GAAG,SAAS,GAAG,gBAAgB,CAAC,IAAI,QAAQ;AAAA,MACpI,GAAG,6BAA6B,iBAAiB,SAAS,GAAG;AAAA,MAC7D,UAAU,CAAC,MAAM;AACf,gBAAQ,SAAS,CAAC;AAClB,YAAI,gBAAgB;AAClB,0BAAgB,SAAS,CAAC;AAAA,MAC9B;AAAA,MACA,YAAY,MAAM;AAChB,YAAI;AACF,qBAAW;AACb,YAAI;AACF,mBAAS;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,IAAI,UAAU;AACrB,UAAM,IAAI,MAAM;AAChB,QAAI;AACF,iBAAW;AACb,QAAI;AACF,eAAS;AACX,WAAO,EAAE,MAAM,MAAM;AAAA,IACrB,EAAE;AAAA,EACJ;AACA,SAAO,CAAC,sBAAsB,CAAC,sBAAsB,gBAAgB,SAAS,QAAQ,MAAM;AAC9F;AAEA,SAAS,uBAAuB;AAC9B,QAAM,EAAE,cAAc,MAAM,IAAI,IAAI,gBAAgB;AACpD,QAAM,OAAO,CAAC,KAAK,OAAO,QAAQ,aAAa,CAAC,GAAG,eAAe;AAChE,UAAM,OAAO,OAAO,GAAG;AACvB,UAAM,cAAc,IAAI,KAAK,MAAM,MAAM;AACzC,QAAI,cAAc,WAAW,WAAW;AACtC,kBAAY,IAAI,KAAK;AACrB;AAAA,IACF;AACA,UAAM,YAAY,aAAa,KAAK,aAAa,OAAO,YAAY,UAAU;AAC9E,gBAAY,MAAM,SAAS;AAAA,EAC7B;AACA,SAAO,EAAE,cAAc,MAAM,KAAK;AACpC;AAEA,SAAS,kBAAkB,kBAAkB,WAAW,CAAC,GAAG,EAAE,cAAc,MAAM,KAAK,IAAI,qBAAqB,GAAG;AACjH,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,cAAc,IAAI,KAAK;AAC7B;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,kBAAY,QAAQ,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,UAAU,MAAM,YAAY,CAAC,EAAE,SAAS;AAAA,IAC5F;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,oBAAoB,CAAC,YAAY;AACrC,QAAI,CAAC,aAAa,CAAC,UAAU,OAAO;AAClC,YAAM,IAAI,MAAM,eAAe,OAAO,kBAAkB;AAC1D,WAAO,UAAU,OAAO;AAAA,EAC1B;AACA,QAAM,QAAQ,CAAC,YAAY;AACzB,QAAI,OAAO,YAAY;AACrB,gBAAU,kBAAkB,OAAO;AACrC,UAAM,aAAa,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/D,UAAI,QAAQ;AACV,eAAO;AACT,aAAO,IAAI;AAAA,QACT,CAAC;AAAA;AAAA,UAEC,KAAK,KAAK,OAAO,kBAAkB,QAAQ,cAAc,qBAAqB,KAAK,QAAQ,GAAG,CAAC,GAAG,OAAO;AAAA;AAAA,MAE7G;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AACjB,mBAAe,kBAAkB;AAviBrC,UAAAC,KAAA;AAwiBM,YAAM,QAAQ,IAAI,UAAU;AAC5B,aAAAA,MAAA,QAAQ,eAAR,gBAAAA,IAAoB,eAApB,wBAAAA;AAAA,IACF;AACA,WAAO,QAAQ,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAAA,EACxC;AACA,QAAM,MAAM,CAAC,YAAY;AACvB,UAAM,cAAc,SAAW,OAAO,IAAI,UAAU,kBAAkB,OAAO;AAC7E,WAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,UAAI,QAAQ;AACV;AACF,WAAK,KAAK,OAAO,kBAAkB;AAAA,QACjC,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,OAAO,SAAS;AAC5B,QAAI;AACJ,QAAI,WAAW;AACb,UAAI,UAAU;AACZ,uBAAe,UAAU;AAC3B,UAAI,CAAC,UAAU,SAAS,UAAU;AAChC,uBAAe,UAAU;AAAA,IAC7B;AACA,QAAI,CAAC,cAAc;AACjB,WAAK;AACL;AAAA,IACF;AACA,UAAM,MAAM,YAAY;AACxB,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,wBAAwB,MAAG;AAhlBjC,MAAAA;AAglBoC,uBAAc,OAAO,kBAAkB,UAAQA,MAAA,YAAY,QAAZ,gBAAAA,IAAiB;AAAA;AACpG,IAAM,sBAAsB,MAAG;AAjlB/B,MAAAA;AAilBkC,uBAAc,OAAO,iBAAiB,UAAQA,MAAA,YAAY,QAAZ,gBAAAA,IAAiB;AAAA;AACjG,IAAM,sBAAsB,MAAG;AAllB/B,MAAAA;AAklBkC,uBAAc,OAAO,gBAAgB,UAAQA,MAAA,YAAY,QAAZ,gBAAAA,IAAiB;AAAA;AAEhG,SAAS,uBAAuB,EAAE,QAAQ,OAAO,UAAU,MAAM,GAAG;AAClE,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,SAAS,IAAI,KAAK;AACxB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,SAAS,MAAM;AACjC,QAAI,SAAS,CAAC,GAAG,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC,CAAC;AAC/C,QAAI,CAAC;AACH,aAAO;AACT,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,OAAO,CAAC;AACxD,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,MAAM,CAAC;AACvD,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,OAAO,CAAC;AACxD,WAAO;AAAA,EACT,CAAC;AACD,QAAM,qBAAqB,SAAS,MAAM;AACxC,UAAM,SAAS,CAAC;AAChB,WAAO,OAAO,QAAQ,MAAM,KAAK;AACjC,QAAI,QAAQ,SAAS,UAAU;AAC7B,aAAO,OAAO,QAAQ,UAAU,OAAO;AACzC,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,OAAO,QAAQ,UAAU,MAAM;AACxC,QAAI,QAAQ,SAAS,UAAU;AAC7B,aAAO,OAAO,QAAQ,UAAU,OAAO;AACzC,eAAW,OAAO,QAAQ;AACxB,UAAI,CAAC,YAAY,MAAM,SAAS,GAAG;AACjC,eAAO,OAAO,GAAG;AAAA,IACrB;AACA,WAAO;AAAA,EACT,CAAC;AACD,MAAI,UAAU,SAAS;AACrB,qBAAiB,QAAQ,cAAc,MAAM,QAAQ,QAAQ,IAAI;AACjE,qBAAiB,QAAQ,cAAc,MAAM;AAC3C,cAAQ,QAAQ;AAChB,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AACA,MAAI,UAAU,QAAQ;AACpB,QAAI,oBAAoB,GAAG;AACzB,uBAAiB,QAAQ,aAAa,MAAM,OAAO,QAAQ,IAAI;AAC/D,uBAAiB,QAAQ,WAAW,MAAM,OAAO,QAAQ,KAAK;AAAA,IAChE;AACA,QAAI,sBAAsB,GAAG;AAC3B,uBAAiB,QAAQ,eAAe,MAAM,OAAO,QAAQ,IAAI;AACjE,uBAAiB,QAAQ,aAAa,MAAM,OAAO,QAAQ,KAAK;AAAA,IAClE;AACA,QAAI,oBAAoB,GAAG;AACzB,uBAAiB,QAAQ,cAAc,MAAM,OAAO,QAAQ,IAAI;AAChE,uBAAiB,QAAQ,YAAY,MAAM,OAAO,QAAQ,KAAK;AAAA,IACjE;AAAA,EACF;AACA,MAAI,UAAU,SAAS;AACrB,qBAAiB,QAAQ,SAAS,MAAM,QAAQ,QAAQ,IAAI;AAC5D,qBAAiB,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,KAAK;AAAA,EAC9D;AACA,QAAM,CAAC,SAAS,QAAQ,OAAO,GAAG,MAAM;AACtC,UAAM,mBAAmB,KAAK;AAAA,EAChC,CAAC;AACH;AAEA,SAAS,uBAAuB,EAAE,KAAK,QAAQ,UAAU,QAAQ,GAAG;AAClE,QAAM,YAAY,MAAM,QAAQ;AAChC;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AACJ,UAAI,CAAC;AACH;AACF,UAAI,UAAU,SAAS;AACrB,YAAI,SAAS;AACb,gBAAQ,QAAQ;AAAA,MAClB;AACA,UAAI,UAAU;AACZ,gBAAQ,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,EAAE,OAAO,MAAM,GAAG;AAC9C;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI;AACF,cAAM,MAAM;AAAA,IAChB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,EAAE,QAAQ,UAAU,QAAQ,GAAG;AAC9D,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,cAAc,UAAU,WAAW,UAAU,cAAc;AAC7D,4BAAwB,QAAQ,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM;AACxD,UAAI,UAAU,SAAS;AACrB,YAAI;AACF,kBAAQ,QAAQ;AAAA,YACb,SAAQ,QAAQ;AAAA,MACvB,WAAW,UAAU,aAAa;AAChC,YAAI,kBAAkB,QAAQ,UAAU;AACtC,kBAAQ,QAAQ;AAAA,iBACT,CAAC,QAAQ;AAChB,kBAAQ,QAAQ;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,kBAAkB,UAAU,UAAU;AAAA,EAC7C,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAClB,GAAG;AACD,MAAI,QAAQ;AACV,2BAAuB,QAAQ;AACjC,MAAI,QAAQ;AACV,yBAAqB,QAAQ;AAC/B,MAAI,QAAQ;AACV,4BAAwB,QAAQ;AAClC,MAAI,QAAQ;AACV,2BAAuB,QAAQ;AACnC;AAEA,SAAS,cAAc,QAAQ,CAAC,GAAG;AACjC,QAAM,QAAQ,SAAS;AAAA,IACrB,GAAG;AAAA,EACL,CAAC;AACD,QAAM,QAAQ,IAAI,CAAC,CAAC;AACpB;AAAA,IACE;AAAA,IACA,MAAM;AACJ,YAAM,SAAS,CAAC;AAChB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,cAAM,YAAY,aAAa,GAAG;AAClC,cAAM,cAAc,eAAe,OAAO,SAAS;AACnD,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,YAAM,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,QAAQ,UAAU;AAC7C;AAAA,IACE,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,OAAO;AACN,UAAI,CAAC;AACH;AACF,eAAS,EAAE;AAAA,IACb;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB;AAAA,EACrB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,SAAS,kBAAkB,QAAQ,CAAC,GAAG,6BAA6B,MAAM;AACxE,QAAM,QAAQ,SAAS,EAAE,GAAG,MAAM,CAAC;AACnC,QAAM,YAAY,IAAI,EAAE;AACxB;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI,SAAS;AACb,UAAI,0BAA0B;AAC9B,UAAI,+BAA+B,OAAO,KAAK,OAAO,KAAK,OAAO,IAAI;AACpE,cAAM,MAAM,CAAC,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,QAAQ,eAAe,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG;AACxG,kBAAU,eAAe,GAAG;AAC5B,kCAA0B;AAAA,MAC5B;AACA,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,YAAI,+BAA+B,QAAQ,OAAO,QAAQ,OAAO,QAAQ;AACvE;AACF,cAAM,YAAY,aAAa,GAAG;AAClC,cAAM,cAAc,eAAe,OAAO,SAAS;AACnD,kBAAU,GAAG,eAAe,GAAG,KAAK,GAAG,IAAI,WAAW;AAAA,MACxD;AACA,UAAI,8BAA8B,CAAC;AACjC,kBAAU;AACZ,gBAAU,QAAQ,OAAO,KAAK;AAAA,IAChC;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,IAAI,KAAK,KAAK,GAAG;AACxC,IAAM,QAAQ,CAAC,eAAe,aAAa,SAAS,UAAU,MAAM;AACpE,IAAM,iBAAiB,CAAC,wBAAwB,KAAK,KAAK,GAAG;AAC7D,MAAM,QAAQ,CAAC,iBAAiB;AAC9B,gBAAc,QAAQ,CAAC,YAAY;AACjC,UAAM,MAAM,eAAe;AAC3B,mBAAe,KAAK,GAAG;AAAA,EACzB,CAAC;AACH,CAAC;AACD,IAAM,mBAAmB,IAAI,IAAI,cAAc;AAC/C,SAAS,gBAAgB,KAAK;AAC5B,SAAO,iBAAiB,IAAI,GAAG;AACjC;AACA,IAAM,uBAAuC,oBAAI,IAAI,CAAC,WAAW,WAAW,SAAS,CAAC;AACtF,SAAS,sBAAsB,KAAK;AAClC,SAAO,qBAAqB,IAAI,GAAG;AACrC;AACA,SAAS,YAAY,SAAS;AAC5B,QAAM,YAAY,CAAC;AACnB,QAAM,QAAQ,CAAC;AACf,SAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChD,QAAI,gBAAgB,GAAG,KAAK,sBAAsB,GAAG;AACnD,gBAAU,GAAG,IAAI;AAAA,QACd,OAAM,GAAG,IAAI;AAAA,EACpB,CAAC;AACD,SAAO,EAAE,WAAW,MAAM;AAC5B;AACA,SAAS,eAAe,SAAS;AAC/B,QAAM,EAAE,WAAW,YAAY,OAAO,OAAO,IAAI,YAAY,OAAO;AACpE,QAAM,EAAE,UAAU,IAAI,kBAAkB,UAAU;AAClD,QAAM,EAAE,MAAM,IAAI,cAAc,MAAM;AACtC,MAAI,UAAU;AACZ,UAAM,MAAM,YAAY,UAAU;AACpC,SAAO,MAAM;AACf;AAEA,SAAS,gBAAgB,QAAQ,QAAQ;AACvC,MAAI;AACJ,MAAI;AACJ,QAAM,EAAE,OAAO,MAAM,IAAI,cAAc;AACvC,sBAAoB,QAAQ,CAAC,OAAO;AAClC,cAAU;AACV,eAAW,OAAO,OAAO,KAAK,UAAU,GAAG;AACzC,UAAI,GAAG,MAAM,GAAG,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,MAAM,gBAAgB,GAAG,KAAK,sBAAsB,GAAG;AACrG;AACF,YAAM,GAAG,IAAI,GAAG,MAAM,GAAG;AAAA,IAC3B;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,IAAI,KAAK;AAAA,IACxE;AACA,QAAI;AACF,aAAO,KAAK;AAAA,EAChB,CAAC;AACD;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI,CAAC,SAAS;AACZ,iBAAS;AACT;AAAA,MACF;AACA,iBAAW,OAAO,OAAQ,SAAQ,MAAM,GAAG,IAAI,OAAO,GAAG;AAAA,IAC3D;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,WAAW;AACjC,QAAM,aAAa,UAAU,KAAK,EAAE,MAAM,QAAQ;AAClD,MAAI,WAAW,WAAW;AACxB,WAAO,CAAC;AACV,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,KAAK;AAC9C,aAAO,OAAO,WAAW,KAAK;AAChC,QAAI,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B,aAAO,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,OAAO,CAAC,KAAK,eAAe;AAC5C,QAAI,CAAC;AACH,aAAO;AACT,UAAM,CAAC,MAAM,cAAc,IAAI,WAAW,MAAM,GAAG;AACnD,UAAM,aAAa,eAAe,MAAM,GAAG;AAC3C,UAAM,SAAS,WAAW,IAAI,CAAC,QAAQ;AACrC,aAAO,YAAY,IAAI,SAAS,GAAG,IAAI,IAAI,QAAQ,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;AAAA,IAC1E,CAAC;AACD,UAAM,QAAQ,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAChD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,mBAAmB,OAAO,WAAW;AAC5C,SAAO,QAAQ,eAAe,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAClE,UAAM,OAAO,CAAC,KAAK,KAAK,GAAG;AAC3B,QAAI,QAAQ,eAAe;AACzB,UAAI,UAAU,GAAG;AACf,aAAK,QAAQ,CAAC,SAAS,MAAM,IAAI,IAAI,CAAC;AACtC;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,WAAW,UAAU,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS;AAClE;AAAA,IACF;AACA,YAAQ,OAAO,WAAW,GAAG,KAAK,EAAE;AACpC,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,UAAM,GAAG,IAAI;AAAA,EACf,CAAC;AACH;AAEA,SAAS,oBAAoB,QAAQ,QAAQ;AAC3C,MAAI;AACJ,MAAI;AACJ,QAAM,EAAE,OAAO,UAAU,IAAI,kBAAkB;AAC/C,sBAAoB,QAAQ,CAAC,OAAO;AAClC,cAAU;AACV,QAAI,GAAG,MAAM;AACX,yBAAmB,OAAO,GAAG,MAAM,SAAS;AAC9C,QAAI;AACF,SAAG,MAAM,YAAY;AACvB,QAAI;AACF,aAAO,KAAK;AAAA,EAChB,CAAC;AACD;AAAA,IACE;AAAA,IACA,CAAC,aAAa;AACZ,UAAI,CAAC,SAAS;AACZ,iBAAS;AACT;AAAA,MACF;AACA,cAAQ,MAAM,YAAY;AAAA,IAC5B;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AAAA,IACL,WAAW;AAAA,EACb;AACF;AAEA,SAASC,eAAc,KAAK;AAC1B,SAAO,OAAO,QAAQ,GAAG;AAC3B;AAEA,SAAS,oBAAoB,QAAQ,eAAe;AAClD,QAAM,mBAAmB,SAAS,CAAC,CAAC;AACpC,QAAM,QAAQ,CAAC,WAAW,OAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,iBAAiB,GAAG,IAAI,KAAK;AACxG,QAAM,EAAE,MAAM,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAM,EAAE,UAAU,IAAI,oBAAoB,QAAQ,KAAK;AACvD;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,MAAAA,eAAc,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9C,cAAM,UAAU,gBAAgB,GAAG,IAAI,YAAY;AACnD,YAAI,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AACnC;AACF,gBAAQ,GAAG,IAAI;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,sBAAoB,QAAQ,MAAM,iBAAiB,MAAM,aAAa,CAAC;AACvE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,WAAW,CAAC,GAAG;AACxC,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,UAAU,IAAI;AACpB,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI,CAAC,QAAQ;AACX;AACF,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,QAAQ,WAAW,CAAC,GAAG,SAAS;AACjD,QAAM,EAAE,iBAAiB,IAAI,oBAAoB,MAAM;AACvD,QAAM,EAAE,SAAS,MAAM,IAAI,kBAAkB,QAAQ;AACrD,QAAM,WAAW,kBAAkB,kBAAkB,QAAQ;AAC7D,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACA,oBAAkB,UAAU,OAAO;AACnC,SAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,SAAS,UAAU;AAC3C,IAAM,qBAAqB,CAAC,WAAW,SAAS,SAAS,WAAW,gBAAgB,eAAe,WAAW,UAAU,WAAW,GAAG,cAAc;AACpJ,SAAS,gBAAgB,KAAK;AAC5B,SAAO,eAAe,SAAS,GAAG;AACpC;AACA,SAAS,gBAAgB,MAAM,aAAa;AAC1C,QAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC;AAC3F,MAAI,QAAQ;AACV,QAAI,OAAO,YAAY,SAAW,OAAO,QAAQ,GAAG;AAClD,kBAAY,QAAQ;AAAA,QAClB,GAAG,YAAY;AAAA,QACf,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AACA,aAAS,OAAO,oBAAoB;AAClC,UAAI,CAAC,UAAU,CAAC,OAAO,GAAG;AACxB;AACF,UAAI,gBAAgB,GAAG,KAAK,OAAO,OAAO,GAAG,MAAM,UAAU;AAC3D,mBAAW,cAAc,CAAC,SAAS,WAAW,aAAa,GAAG;AAC5D,gBAAM,gBAAgB,YAAY,MAAM,UAAU;AAClD,cAAI,iBAAiB;AACnB;AACF,wBAAc,eAAd,cAAc,aAAe,CAAC;AAC9B,wBAAc,WAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5C;AACA;AAAA,MACF;AACA,UAAI,SAAW,OAAO,GAAG,CAAC,GAAG;AAC3B,cAAM,OAAO,OAAO,GAAG;AACvB,YAAI,QAAQ;AACV,gBAAM;AACR,oBAAY,MAAM,GAAG,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,UAAU,UAAU,WAAW,OAAO;AAC7C,QAAM,WAAW,CAAC,IAAI,SAAS,SAAS;AACtC,UAAM,MAAM,QAAQ,SAAS,OAAO,QAAQ,UAAU,WAAW,QAAQ,QAAQ,KAAK;AACtF,QAAI,OAAO,YAAY,GAAG;AACxB,kBAAY,GAAG,EAAE,KAAK;AACxB,UAAM,iBAAiB,WAAW,gBAAgB,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,YAAY,CAAC;AACxF,UAAM,cAAc,IAAI,cAAc;AACtC,QAAI,OAAO,QAAQ,UAAU;AAC3B,kBAAY,QAAQ,QAAQ;AAC9B,oBAAgB,MAAM,WAAW;AACjC,UAAM,gBAAgB,EAAE,gBAAgB,MAAM,gBAAgB,MAAM,cAAc,MAAM,iBAAiB,MAAM;AAC/G,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,OAAG,iBAAiB;AACpB,QAAI;AACF,kBAAY,GAAG,IAAI;AAAA,EACvB;AACA,QAAM,UAAU,CAAC,IAAI,UAAU,UAAU;AACvC,OAAG,kBAAkB,wBAAwB,GAAG,cAAc;AAAA,EAChE;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,YAAY,SAAS,MAAM;AACzB,UAAI,EAAE,SAAS,eAAe,IAAI,QAAQ,SAAS,SAAQ,6BAAM,UAAS,CAAC;AAC3E,uBAAiB,MAAM,cAAc;AACrC,YAAM,UAAU,KAAK,CAAC,IAAG,qCAAU,YAAW,CAAC,GAAG,kBAAkB,CAAC,CAAC;AACtE,UAAI,CAAC,WAAW,OAAO,KAAK,OAAO,EAAE,WAAW;AAC9C;AACF,YAAM,QAAQ,eAAe,OAAO;AACpC,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,OAAO;AAAA,EACX,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AACA,IAAM,cAAc;AAAA,EAClB,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AACF;AAEA,IAAM,MAAM;AAAA,EACV,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AAEA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,oBAAoB;AAAA,EACxB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,oBAAoB;AAAA,EACxB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,cAAc;AAAA,EAClB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,yBAAyB;AAAA,EAC7B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AAEA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,QAAQ,KAAK;AACpB,QAAMR,KAAI;AACV,QAAMC,KAAI;AACV,QAAM,IAAI,IAAI,OAAOD,GAAE,MAAM,EAAE,EAAE,KAAK,GAAG,GAAG,GAAG;AAC/C,SAAO,IAAI,SAAS,EAAE,QAAQ,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,YAAY,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,GAAG,CAACE,OAAMD,GAAE,OAAOD,GAAE,QAAQE,EAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAClP;AAv7CA;AAy7CA,IAAM,iBAAiB;AAAA,IACrB,iBAAY,QAAZ,mBAAiB,UAAS,gBAAgB,wBAAwB;AACpE;AAEA,IAAM,uBAAuB;AAAA;AAAA,EAE3B,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACZ;AACF;AACA,SAASO,UAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AACA,SAAS,MAAM,GAAG;AAChB,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,EAAE,IAAI,KAAK;AAAA,EACpB;AACA,MAAIA,UAAS,CAAC,GAAG;AACf,UAAM,MAAM,CAAC;AACb,eAAW,OAAO,GAAG;AACnB,UAAI,GAAG,IAAI,MAAM,EAAE,GAAG,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,OAAO;AA3gDrC,MAAAF;AA4gDE,QAAM,YAAY,SAAS,CAAC,CAAC;AAC7B,QAAM,gBAAgB,OAAO,gBAAgB,CAAC,CAAC;AAC/C,QAAM,SAAS,SAAS,MAAM;AAC5B,QAAI,MAAM,UAAU,MAAM;AACxB,aAAO,CAAC;AAAA,IACV;AACA,QAAI,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAC1D,aAAO,gBAAgB,MAAM,aAAa,EAAE,MAAM,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,MAAM,UAAU,SAAS;AAC3B,aAAO,gBAAgB,QAAQ,MAAM,MAAM,CAAC;AAAA,IAC9C;AACA,WAAO,CAAC;AAAA,EACV,CAAC;AACD,QAAM,cAAc,SAAS,OAAO;AAAA,IAClC,SAAS,MAAM;AAAA,IACf,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,IACb,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM;AAAA,IACf,QAAQ,MAAM;AAAA,IACd,SAAS,MAAM;AAAA,EACjB,EAAE;AACF,WAAS,uBAAuB,QAAQ,QAAQ;AAC9C,eAAW,iBAAiB,CAAC,SAAS,UAAU,GAAG;AACjD,UAAI,OAAO,aAAa,KAAK;AAC3B;AACF,YAAM,wBAAwB,OAAO;AAAA,QACnC,OAAO,aAAa;AAAA,MACtB;AACA,iBAAW,cAAc,CAAC,SAAS,WAAW,aAAa,GAAG;AAC5D,cAAM,gBAAgB,OAAO,UAAU;AACvC,YAAI,iBAAiB;AACnB;AACF,sBAAc,eAAd,cAAc,aAAe,CAAC;AAC9B,sBAAc,WAAW,aAAa,IAAI;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,SAAS,MAAM;AAClC,UAAM,SAAS;AAAA,MACb,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM,YAAY,CAAC;AAAA,IACrB;AACA,WAAO,uBAAuB,EAAE,GAAG,OAAO,GAAG,KAAK;AAAA,EACpD,CAAC;AACD,QAAIA,MAAA,YAAY,QAAZ,gBAAAA,IAAiB,UAAS,eAAe;AAC3C,QAAI,MAAM,UAAU,SAAQ,mCAAU,MAAM,YAAW,SAAQ,+CAAgB,MAAM,YAAW,MAAM;AACpG,cAAQ,KAAK,8BAA8B,MAAM,MAAM,eAAe;AAAA,IACxE;AACA,UAAM,kBAAkB,CAAC,aAAa;AAlkD1C,UAAAA;AAmkDM,WAAIA,MAAA,SAAS,aAAT,gBAAAA,IAAmB,SAAS;AAC9B,iBAAS,IAAI,SAAS;AAAA,MACxB;AACA,eAAS,MAAM;AAtkDrB,YAAAA,KAAA;AAukDQ,aAAIA,MAAA,SAAS,aAAT,gBAAAA,IAAmB;AACrB,mBAAS,MAAM,OAAO;AACxB,aAAI,cAAS,aAAT,mBAAmB;AACrB,mBAAS,MAAM,SAAS;AAC1B,aAAI,cAAS,aAAT,mBAAmB;AACrB,mBAAS,MAAM,aAAa;AAAA,MAChC,CAAC;AAAA,IACH;AACA,cAAU,MAAM;AACd,iBAAW,OAAO,WAAW;AAC3B,wBAAgB,UAAU,GAAG,CAAC;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,gBAAgB,MAAM,OAAO,OAAO;AArlD/C,QAAAA;AAslDI,SAAK,UAAL,KAAK,QAAU,CAAC;AAChB,KAAAA,MAAA,KAAK,OAAM,UAAXA,IAAW,QAAU,CAAC;AACtB,SAAK,MAAM,QAAQ,EAAE,GAAG,KAAK,MAAM,OAAO,GAAG,MAAM;AACnD,UAAM,sBAAsB;AAAA,MAC1B,MAAM,aAAa,KAAK;AAAA,MACxB,KAAK;AAAA,IACP;AACA,SAAK,MAAM,iBAAiB,CAAC,EAAE,GAAG,MAAM;AACtC,gBAAU,KAAK,IAAI;AAAA,QACjB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,iBAAiB,CAAC,EAAE,GAAG,MAAM;AACtC,YAAM,SAAS,eAAe,UAAU,KAAK,EAAE,KAAK;AACpD,iBAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/C,WAAG,MAAM,GAAG,IAAI;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,gBAAgB;AAAA,EACtC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,SAAS;AACvB,UAAM,EAAE,cAAc,gBAAgB,IAAI,qBAAqB,KAAK;AACpE,WAAO,MAAM;AACX,YAAM,QAAQ,eAAe,aAAa,MAAM,WAAW,CAAC,CAAC;AAC7D,YAAM,OAAO,EAAE,MAAM,IAAI,QAAQ,KAAK;AACtC,sBAAgB,MAAM,GAAG,KAAK;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAED,IAAM,uBAAuB,gBAAgB;AAAA,EAC3C,MAAM;AAAA,EACN,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,SAAS;AACvB,UAAM,EAAE,cAAc,gBAAgB,IAAI,qBAAqB,KAAK;AACpE,WAAO,MAAM;AAlpDjB,UAAAA;AAmpDM,YAAM,QAAQ,eAAe,aAAa,MAAM,WAAW,CAAC,CAAC;AAC7D,YAAM,UAAQA,MAAA,MAAM,YAAN,gBAAAA,IAAA,gBAAqB,CAAC;AACpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,IAAI,MAAM,CAAC;AACjB,YAAI,EAAE,SAAS,YAAY,MAAM,QAAQ,EAAE,QAAQ,GAAG;AACpD,YAAE,SAAS,QAAQ,SAAS,iBAAiB,OAAO,OAAO;AACzD,gBAAI,SAAS;AACX;AACF,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,+BAAiB,OAAO,KAAK;AAC7B;AAAA,YACF;AACA,gBAAI,OAAO,UAAU,UAAU;AAC7B,8BAAgB,OAAO,OAAO,KAAK;AAAA,YACrC;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,0BAAgB,GAAG,GAAG,KAAK;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,MAAM,IAAI;AACZ,eAAO,EAAE,MAAM,IAAI,QAAQ,KAAK;AAAA,MAClC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAAe;AAAA,EACnB,QAAQ,KAAK,SAAS;AAhrDxB,QAAAA;AAirDI,QAAI,UAAU,UAAU,UAAU,CAAC;AACnC,QAAI,CAAC,WAAW,WAAW,CAAC,QAAQ,gBAAgB;AAClD,iBAAW,OAAO,SAAS;AACzB,cAAM,SAAS,QAAQ,GAAG;AAC1B,YAAI,UAAU,UAAU,QAAQ,GAAG,CAAC,IAAI,UAAU,QAAQ,IAAI,CAAC;AAAA,MACjE;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,YAAY;AACjC,iBAAW,OAAO,QAAQ,YAAY;AACpC,cAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,YAAI,CAAC,SAAS,aAAWA,MAAA,YAAY,QAAZ,gBAAAA,IAAiB,UAAS,eAAe;AAChE,kBAAQ;AAAA,YACN,2BAA2B,GAAG;AAAA,UAChC;AAAA,QACF;AACA,YAAI,UAAU,UAAU,GAAG,IAAI,UAAU,UAAU,IAAI,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,QAAQ,gBAAgB,mCAAS,UAAU;AAC/C,QAAI,UAAU,UAAU,eAAe;AACvC,QAAI,UAAU,eAAe,oBAAoB;AAAA,EACnD;AACF;AAEA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,OAAO;AACb,SAAO,KAAK,UAAU,UAAU,OAAO,KAAK,UAAU,cAAc,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,cAAc,KAAK,WAAW,UAAU,MAAM,KAAK,MAAM;AAC1K;AAEA,SAAS,aAAa;AACpB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQG,SAAQ;AACjC,QAAM,EAAE,MAAM,IAAI,IAAI,gBAAgB;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK,CAAC,eAAe,QAAQ;AAAA,MAC3B,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,cAAM,cAAc,IAAI,KAAK,OAAO,GAAG,GAAG,MAAM;AAChD,eAAO,YAAY,MAAM,CAAC,eAAe;AACvC,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,YACN,GAAGA,WAAU,qBAAqB,KAAK,KAAK;AAAA,UAC9C;AACA,iBAAO,QAAQ;AAAA,YACb,MAAM,YAAY,IAAI;AAAA,YACtB,IAAI;AAAA,YACJ,UAAU,YAAY,YAAY;AAAA,YAClC,UAAU,CAAC,MAAM,YAAY,IAAI,CAAC;AAAA,YAClC;AAAA,YACA,GAAG;AAAA,UACL,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM,gBAAgB,cAAc,oCAAoC,OAAO;AAC/E,SAAO,SAAS,MAAM,cAAc,KAAK;AAC3C;", "names": ["c", "_a", "_a", "px", "c", "cb", "_a", "test", "window", "window", "runNextFrame", "isProcessing", "process", "timestamp", "clamp", "clamp", "undampedFreq", "a", "b", "c", "keys", "_a", "a", "b", "progress", "clamp", "clamp", "a", "b", "c", "alpha", "clamp", "b", "a", "_a", "v", "number", "alpha", "color", "a", "b", "analyse", "clamp", "keys", "_a", "_a", "identity", "a", "b", "c", "timestamp", "keys", "keyframes", "getAnimatableNone", "_a", "objectEntries", "isObject", "spring"]}