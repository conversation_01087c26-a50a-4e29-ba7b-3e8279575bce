## 用augment+vue实现UI设计

| 不要做                                                       | 要做                                                         |
| ------------------------------------------------------------ | ------------------------------------------------------------ |
| augment根据html和css来写代码，这是因为这些代码会极大污染上下文把augment带偏 | 给出ui设计文档+业务逻辑加上api文档+商量出来的技术栈          |
| 直接用原生的css和js，代码量越大，问题越大                    | 特别说明vue的版本，ai遇到代码问题就自己查context7或是搜索网络 |
|                                                              |                                                              |

