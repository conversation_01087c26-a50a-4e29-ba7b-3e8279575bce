<template>
  <div 
    class="typing-indicator"
    v-motion
    :initial="{ x: -50, opacity: 0 }"
    :enter="{ x: 0, opacity: 1, transition: { duration: 300 } }"
  >
    <div class="message-layout">
      <div class="avatar ai-avatar">
        AI
      </div>
      <div class="typing-bubble">
        <div class="typing-dots">
          <div class="dot" :style="{ animationDelay: '0ms' }"></div>
          <div class="dot" :style="{ animationDelay: '150ms' }"></div>
          <div class="dot" :style="{ animationDelay: '300ms' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需额外逻辑
</script>

<style scoped>
.typing-indicator {
  width: 100%;
}

.message-layout {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ai-avatar {
  background: oklch(0.5500 0.1800 230.0000);
  color: white;
}

.typing-bubble {
  background: oklch(0.9800 0.0050 220.0000);
  padding: 12px 16px;
  border-radius: 8px;
  border-bottom-left-radius: 4px;
  max-width: 70px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.dot {
  width: 6px;
  height: 6px;
  background: oklch(0.5000 0.0100 264.0000);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>
